<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no" />
<block name="title">
<title>汉高微信后台管理</title>
</block>
<link rel="stylesheet" href="/ug/admin/css/bootstrap.min.css">
<link rel="stylesheet" href="/ug/admin/css/dialog.css">
<link rel="stylesheet" href="/ug/admin/css/datatable.css">
<style type="text/css">
.query-box {
	padding-bottom: 10px;
}

.list-group {
	width: 500px;
}
</style>
</head>
<body>
	<div class="container-fluid">
		<h2 id="list-group-linked">请选择您要管理的企业</h2>
		<div class="list-group">
			<volist name="rows" id="vo">
			<button type="button" class="list-group-item" _id="{$vo.id}">{$vo.name}</button>
			</volist>
		</div>
	</div>
</body>
<script type="text/javascript" src="/ug/js/jquery.min.js"></script>
<script type="text/javascript" src="/ug/js/plugincore.js"></script>
<script type="text/javascript" src="/ug/admin/js/dialog.js"></script>
<script type="text/javascript">
	$(function() {
		$('.list-group-item').on('click', function() {
			var id = $(this).attr('_id');
			var turl = '{$url.slt}';
			$.ajaxExt(turl, {
				id : id
			}, function(jo) {
				if (jo.isOk()) {
					window.location.reload();
				} else {
					$.alert(jo.getMessage());
				}
			});
		});
	});
</script>
</html>