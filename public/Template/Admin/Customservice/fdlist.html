<extend name="Base@Common/listtable"/>
<block name="title">
    <title>功能列表</title>
</block>
<block name="js"> <script type="text/javascript">
    inf_dtbl.cols = [ {
        col : 'c1',
        tit : '问题'
    },{
        col : 'c2',
        tit : '答案'
    }, {
        col : 'c3',
        tit : '创建时间',
        sortable : true,
        rep : function(d) {
            if (d.c3 != '0')
                return d.c3.date('yyyy-MM-dd hh:mm:ss');
        }
    }, ];

    inf_dtbl.menus.rows.push({
        tag : 'editFaq',
        title : '编辑',
        icon : 'glyphicon glyphicon-paperclip'
    });
    inf_ext.menus.callback = function(tag, d){
        switch(tag){
            case 'editFaq':
                $("input[name='fdid']").val(d.c0);
                $("input[name='question']").val(d.c1);
                $("textarea[name='answer']").val(d.c2);
//                $("#editdlg").show();
                $('#editdlg').dialog('编辑分类', editSave);
                break;
            default :
                break;
        }
    };

    $(function() {
        $('#btn_add').on('click', function() {
            $('#adddlg').dialog('新增问题', addSave);
        });
    });
    function editSave(){
        var pdata = {};
        $.formvals($(this), 'cid', pdata);
        for ( var k in pdata) {
            if (pdata[k] === '') {
                var anchor = $(this).find('.anchor_' + k);
                anchor.poptip('请输入或选择信息');
                anchor.trigger('focus');
                return false;
            }
        }
        var turl = '{$url.editsave}';
        $.ajaxExt(turl, pdata, function(jo) {
            if (jo.isOk()) {
                window.location.reload();
            } else {
                $.alert(jo.getMessage());
            }
        });
    }
    function formIgnore(k, pdata) {
        switch (k) {
            case 'c1':
                pdata[k] = 0;
                return true;
        }
    }

    function addSave() {
        var pdata = {};
        $.formvals($(this), 'cid', pdata);
        for ( var k in pdata) {
            if (pdata[k] === '' || pdata[k] === '0') {
                if (k == 'c1') {
                    pdata[k] = '0';
                } else {
                    var anchor = $(this).find('.anchor_' + k);
                    anchor.poptip('请输入或选择信息');
                    anchor.trigger('focus');
                    return false;
                }
            }
        }
        var turl = '{$url.addsave}';
        $.ajaxExt(turl, pdata, function(jo) {
            if (jo.isOk()) {
                window.location.reload();
            } else {
                $.alert(jo.getMessage());
            }
        });
    }
</script> </block>
<block name="query_ui">
    <div class="form-group">
        <!--<label>问题</label> --><input type="text" class="form-control input-sm" cid="q{$k++}" placeholder="搜索问题名称">
    </div>
</block>
<block name="tool_box">
    <button id="btn_add" type="button" class="btn btn-default btn-sm">
        <span class="glyphicon glyphicon-plus"></span>&nbsp;新增
    </button>
</block>
<block name="free_area">
    <div id="adddlg" style="display: none;">
        <div>
            <div>
                <form class="form-horizontal">
                    <input type="hidden" name="fdid" cid="id" value="{$id}">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">问题</label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control anchor_c0" cid="c0">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label">答案</label>
                        <div class="col-sm-8">
                            <textarea class="form-control anchor_c1" cid="c1" rows="3"></textarea>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div id="editdlg" style="display: none;">
        <div>
            <div>
                <form class="form-horizontal">
                    <input type="hidden" name="fdid" cid="id" value="">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">问题</label>
                        <div class="col-sm-8">
                            <input type="text" name="question" class="form-control anchor_c0" cid="c0">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label">答案</label>
                        <div class="col-sm-8">
                            <textarea name="answer" class="form-control anchor_c1" cid="c1" rows="3"></textarea>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</block>
