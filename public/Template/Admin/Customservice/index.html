<extend name="Base@Common/listtable"/>
<block name="title">
	<title>功能列表</title>
</block>
<block name="js">
	<script type="text/javascript">
		inf_dtbl.cols = [{
			col: 'c1',
			tit: '类别名'
		}, {
			col: 'c2',
			tit: '描述'
		}, {
			col: 'c3',
			tit: '创建时间',
			sortable: true,
			rep: function(d) {
				if(d.c3 != '0')
					return d.c3.date('yyyy-MM-dd hh:mm:ss');
			}
		}, ];

		inf_dtbl.menus.rows.push({
			tag: 'toRelation',
			title: '配置问题',
			icon: 'glyphicon glyphicon-paperclip'
		});
		inf_dtbl.menus.rows.push({
			tag: 'editFaq',
			title: '编辑',
			icon: 'glyphicon glyphicon-paperclip'
		});
		inf_ext.menus.callback = function(tag, d) {
			switch(tag) {
				case 'toRelation':
					$.openTab('{:U('fdlist')}?id=' + d.id, d.c1 + ' 分类');
					break;
				case 'editFaq':
					$("input[name='faqid']").val(d.c0);
					$("input[name='name']").val(d.c1);
					$("textarea[name='desc']").val(d.c2);
					$("input[name='sort']").val(d.c4);
					//                $("#editdlg").show();
					$('#editdlg').dialog('编辑分类', editSave);
					break;
				default:
					break;
			}
		};

		$(function() {
			$('#btn_add').on('click', function() {
				$('#adddlg').dialog('新增分类', addSave);
			});
		});

		function editSave() {
			var pdata = {};
			$.formvals($(this), 'cid', pdata);
			for(var k in pdata) {
				if(pdata[k] === '') {
					var anchor = $(this).find('.anchor_' + k);
					anchor.poptip('请输入或选择信息');
					anchor.trigger('focus');
					return false;
				}
			}
			var turl = '{$url.editsave}';
			$.ajaxExt(turl, pdata, function(jo) {
				if(jo.isOk()) {
					window.location.reload();
				} else {
					$.alert(jo.getMessage());
				}
			});
		}

		function formIgnore(k, pdata) {
			switch(k) {
				case 'c1':
					pdata[k] = 0;
					return true;
			}
		}

		function addSave() {
			var pdata = {};
			$.formvals($(this), 'cid', pdata);
			for(var k in pdata) {
				if(pdata[k] === '' || pdata[k] === '0') {
					if(k == 'c1') {
						pdata[k] = '0';
					} else {
						var anchor = $(this).find('.anchor_' + k);
						anchor.poptip('请输入或选择信息');
						anchor.trigger('focus');
						return false;
					}
				}
			}
			var turl = '{$url.addsave}';
			$.ajaxExt(turl, pdata, function(jo) {
				if(jo.isOk()) {
					window.location.reload();
				} else {
					$.alert(jo.getMessage());
				}
			});
		}
		$(function() {
			//获取邮箱和电话
			var turl = '/admin/customservice/contact';
			$.ajaxExt(turl, {}, function(jo) {
				if(jo.isOk()) {
					//                window.location.reload();
					if(jo.data) {
						$('#email').val(jo.data.email)
						$('#tel').val(jo.data.tel)
					}
				} else {
					$.alert(jo.getMessage());
				}
			});
		})
		$("#saveContact").on('click', function() {
			var turl = '/admin/customservice/contactsave';
			var pdata = {};
			pdata.email = $("#email").val();
			pdata.tel = $("#tel").val();
			$.ajaxExt(turl, pdata, function(jo) {
				if(jo.isOk()) {
					window.location.reload();
				} else {
					$.alert(jo.getMessage());
				}
			});
		})
	</script>
</block>
<block name="query_ui">
	<style>
		#btn_add,
		#query_btn {
			margin-top: 50px;
		}
		
		#connect {
			position: absolute;
			top: 20px;
			left: 25px;
		}
		
		.form-inline>.form-group>label {
			min-width: 70px;
			text-align: right;
		}
	</style>
	<div class="form-group" style="margin-top: 50px">
		<!--<label>分类名</label> --><input type="text" class="form-control input-sm" cid="q{$k++}" placeholder="搜索分类名">
	</div>
</block>
<block name="subject_area">
	<div id="connect">
		<div class="form-group">
			<form class="form-inline">
				<div class="form-group">
					<label>客服电话</label>
					<input type="text" class="form-control input-sm" id="tel" placeholder="请输入客服电话">
				</div>
				<div class="form-group">
					<label>客服邮箱</label>
					<input type="email" class="form-control input-sm" id="email" placeholder="请输入客服邮箱">
				</div>
				<button type="button" class="btn btn-sm btn-default admin-btn-tab" id="saveContact"><i class="glyphicon glyphicon-saved"></i>保存</button>
			</form>
		</div>
	</div>
</block>
<block name="tool_box">
	<button id="btn_add" type="button" class="btn btn-sm btn-default admin-btn-tab">
        <span class="glyphicon glyphicon-plus"></span>&nbsp;新增
    </button>
</block>
<block name="free_area">
	<div id="adddlg" style="display: none;">
		<div>
			<div>
				<form class="form-horizontal">
					<div class="form-group">
						<label class="col-sm-4 control-label">分类名</label>
						<div class="col-sm-8">
							<input type="text" class="form-control anchor_c0" cid="c0">
						</div>
					</div>
					<div class="form-group">
						<label class="col-sm-4 control-label">分类说明</label>
						<div class="col-sm-8">
							<textarea class="form-control anchor_c1" cid="c1" rows="3"></textarea>
						</div>
					</div>
					<div class="form-group">
						<label class="col-sm-4 control-label">排序值</label>
						<div class="col-sm-8">
							<input type="text" class="form-control anchor_c2" cid="c2">
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>

	<div id="editdlg" style="display: none;">
		<div>
			<div>
				<form class="form-horizontal">
					<input type="hidden" name="faqid" cid="id">
					<div class="form-group">
						<label class="col-sm-4 control-label">分类名</label>
						<div class="col-sm-8">
							<input type="text" name="name" class="form-control anchor_c0" cid="c0">
						</div>
					</div>
					<div class="form-group">
						<label class="col-sm-4 control-label">分类说明</label>
						<div class="col-sm-8">
							<textarea name="desc" class="form-control anchor_c1" cid="c1" rows="3"></textarea>
						</div>
					</div>
					<div class="form-group">
						<label class="col-sm-4 control-label">排序值</label>
						<div class="col-sm-8">
							<input type="text" name="sort" class="form-control anchor_c2" cid="c2">
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
</block>