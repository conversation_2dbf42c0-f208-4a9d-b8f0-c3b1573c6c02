<extend name="Base@Common/listtable"/>
<block name="title">
<title>评价列表</title>
</block>
<block name="js"> <script type="text/javascript">

	inf_dtbl.cols = [{
		col : 'c0',
		tit : '评价时间',
		rep : function(d) {
			return d.c0.date('yyyy-MM-dd hh:mm:ss');
		}
	}, {
		col : 'c1',
		tit : '评价内容'
	}, {
		tit : '回复',
		rep : function(d) {
			if(d.reply){
				var html='';
				for(var i=0;i<d.reply.length;i++){
					if(i>0){
						html += '<br>';
					}
					html += d.reply[i].c1.date('MM-dd hh:mm:ss')+'&nbsp;&nbsp;';
					html += d.reply[i].c0;
				}
				return html;
			}
		}
	}, {
		col : 'c2',
		tit : '状态',
		rep : function(d) {
			var str = '';
			if (d.c2 == 0) {
				str = '未审核';
			} else if (d.c2 == 1) {
				str = '审核通过';
			} else {}
			return str;
		}
	}];
	

	inf_dtbl.menus.dynamic = function(d) {
		var menus = [ ];
		if (d.c2 == '0') {
			menus.push({
				tag : 'del',
				title : '删除',
				icon : 'glyphicon glyphicon-trash'
			});
			menus.push({
				tag : 'check',
				title : '审核通过',
				icon : 'glyphicon glyphicon-ok'
			});
		}else{
			menus.push({
				tag : 'reply',
				title : '回复',
				icon : 'glyphicon glyphicon-comment'
			});
		}
		return menus;
	};
	
	inf_ext.menus.callback = function(tag, d){
		switch(tag){
			case 'reply':
				reply(d, $(this));
				break;
			case 'check':
				checkStatus(d, $(this));
				break;
			default :
				break;
		}
	};

	function reply(data, obj) {
		$('#replydiv').dialog('回复', function() {
			var pdata = {};
			pdata.c1 = data.id;
			$.formvals($(this), 'cid', pdata);
			for ( var k in pdata) {
				if (pdata[k] === '' || pdata[k] === '0') {
					var anchor = $(this).find('.anchor_' + k);
					anchor.poptip('请输入回复内容');
					anchor.trigger('focus');
					return false;
				}
			}

			var turl = '{$url.reply}';
			$.ajaxExt(turl, pdata, function(jo) {
				if (jo.isOk()) {
					window.location.reload();
				} else {
					$.alert(jo.getMessage());
				}
			});
		})
	}

	function checkStatus(data, obj)
	{
		$.confirm('确定审核通过吗？', function() {
			var turl = '{$url.check}';
			$.ajaxExt(turl, {id: data.id}, function(jo) {
				if (jo.isOk()) {
					window.location.reload();
				} else {
					$.alert(jo.getMessage());
				}
			});
		});
	}
</script> 
</block>


<block name="free_area">
<div id="replydiv" style="display: none;">
	<div>
		<div>
			<form class="form-horizontal">
				<div class="form-group">
					<label class="col-sm-4 control-label">回复内容</label>
					<div class="col-sm-8">
						<textarea class="form-control anchor_c0" cid="c0" rows="3"></textarea>
					</div>
				</div>
			</form>
		</div>
	</div>
</div>
</block>