<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"/>
    <title>汉高微信后台管理</title>
    <link rel="stylesheet" href="/ug/admin/css/bootstrap.min.css">
    <link rel="stylesheet" href="/ug/admin/css/tabpage.css">
    <link rel="stylesheet" href="/ug/admin/css/dialog.css">
    <style type="text/css">
        .panel {
            width: 500px;
        }

        .panel .table {
            margin-bottom: 0px;
            table-layout: fixed;
        }

        .panel .table th {
            width: 120px;
        }

        .panel .table tbody tr:first-child > * {
            border-top: 0px;
        }

        .panel .panel-body {
            padding: 5px 15px;
        }

        .qrctxt > div {
            display: inline-block;
            background: rgb(64, 167, 231);
            color: #fff;
            font-size: 16px;
            padding: 10px;
            line-height: 20px;
        }

        .qrctxt a {
            text-decoration: none;
            color: #fff;
        }

        .filebtn input {
            position: absolute;
            right: 0px;
            top: 0px;
            opacity: 0;
            -ms-filter: 'alpha(opacity=0)';
            font-size: 200px;
        }

        .table > tbody > tr > td,
        .table > tbody > tr > th,
        .table > tfoot > tr > td,
        .table > tfoot > tr > th,
        .table > thead > tr > td,
        .table > thead > tr > th {
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
        }
    </style>
</head>

<body>
<div class="container-fluid">
    <div class="panel panel-default">
        <div class="panel-heading">
            <h3 class="panel-title">企业基本信息</h3>
        </div>
        <div class="panel-body">
            <table class="table table-hover">
                <tbody>
                <tr>
                    <th>企业名</th>
                    <td><input type="text" class="form-control anchor_c0" cid="c0"></td>
                </tr>
                <tr>
                    <th>管理者帐号</th>
                    <td><input type="text" class="form-control anchor_c1" cid="c1"></td>
                </tr>
                <tr>
                    <th>密码</th>
                    <td><input type="password" class="form-control anchor_c2" cid="c2"></td>
                </tr>
                <tr>
                    <th>确认密码</th>
                    <td><input type="password" class="form-control anchor_c3" cid="c3"></td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div>
        <button id="btnok" type="button" class="btn btn-default-bg">保存</button>
    </div>
</div>

</body>
<script type="text/javascript" src="/ug/js/jquery.min.js"></script>
<script type="text/javascript" src="/ug/js/plugincore.js"></script>
<script type="text/javascript" src="/ug/admin/js/dialog.js"></script>
<script type="text/javascript" src="/ug/admin/js/formvals.js"></script>
<script type="text/javascript">
    var url = "#{$url}#";
    $ (function () {
        var w = $ (this);
        $ ("#btnok").on ('click', function () {
            var turl = url.doadd;
            var pdata = {};
            $.formvals (w, 'cid', pdata)
            for (var k in pdata) {
                if (pdata[k] === '' || pdata[k] === '0') {
                    var anchor = $ (this).find ('.anchor_' + k);
                    anchor.poptip ('请输入信息');
                    anchor.trigger ('focus');
                    return false;
                }
            }
            if (pdata.c2 !== pdata.c3) {
                var anchor = w.find ('.anchor_c3');
                anchor.poptip ('两次密码输入不一致');
                anchor.trigger ('focus');
                return false;
            }
            $.ajaxExt (turl, pdata, function (jo) {
                if (jo.isOk ()) {
                    $.alert (jo.getMessage ());
                } else {
                    $.alert (jo.getMessage ());
                }
            });
        });
    });
</script>
</html>