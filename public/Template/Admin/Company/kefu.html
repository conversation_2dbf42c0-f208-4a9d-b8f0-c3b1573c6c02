<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no" />
<title>汉高微信后台管理</title>
<link rel="stylesheet" href="/ug/admin/css/bootstrap.min.css">
<link rel="stylesheet" href="/ug/admin/css/tabpage.css">
<link rel="stylesheet" href="/ug/admin/css/dialog.css">
<style type="text/css">
.query-box {
	padding-bottom: 10px;
}

.panel-default {
	width: 720px;
}

.panel .table {
	margin-bottom: 0px;
}

.panel .table th {
	width: 120px;
}

.panel .table tbody tr:first-child>* {
	border-top: 0px;
}

.panel .panel-body {
	padding: 5px 15px;
}

.panel-title .btn-group {
	float: right;
	padding-top: 2px;
	margin-top: -3px;
}

.panel-title .btn-group {
	float: right;
}

.panel-title .btn-xs {
	padding-top: 2px;
	margin-top: -3px;
}

.qrcbox {
	position: relative;
	width: 300px;
	height: 300px;
}

.qrcimg {
	margin: 0 auto;
	text-align: center;
}

.qrctxt {
	position: absolute;
	z-index: 2;
	left: 0px;
	top: 0px;
	right: 0px;
	bottom: 0px;
	text-align: center;
	vertical-align: middle;
	line-height: 280px;
}

.qrctxt>div {
	display: inline-block;
	background: rgb(64, 167, 231);
	color: #fff;
	font-size: 16px;
	padding: 10px;
	line-height: 20px;
}

.qrctxt a {
	text-decoration: none;
	color: #fff;
}
</style>
</head>
<body>
	<div class="container-fluid">
		<div class="panel panel-default">
			<div class="panel-heading">
				<h3 class="panel-title">企业基本信息</h3>
			</div>
			<div class="panel-body">
				<table class="table table-hover">
					<tbody>
						<tr>
							<th>企业名</th>
							<td>{$company.c0}</td>
						</tr>
						<tr>
							<th>企业标识</th>
							<td>{$company.c1}</td>
						</tr>
						<tr>
							<th>管理者</th>
							<td>{$company.c2}</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</body>
<script type="text/javascript" src="/ug/js/jquery.min.js"></script>
<script type="text/javascript" src="/ug/js/plugincore.js"></script>
<script type="text/javascript" src="/ug/admin/js/dialog.js"></script>
<script type="text/javascript" src="/ug/admin/js/formvals.js"></script>
<script type="text/javascript">
</script>
</html>