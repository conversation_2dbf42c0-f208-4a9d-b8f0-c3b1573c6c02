<!DOCTYPE html>
<html lang="zh-CN">

	<head>
		<meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
		<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no" />
		<title>汉高微信后台管理</title>
		<link rel="stylesheet" href="/ug/admin/css/bootstrap.min.css">
		<link rel="stylesheet" href="/ug/admin/css/tabpage.css">
		<link rel="stylesheet" href="/ug/admin/css/dialog.css">
		<style type="text/css">
			.query-box {
				padding-bottom: 10px;
			}
			
			.panel .table {
				margin-bottom: 0px;
				table-layout: fixed;
			}
			
			.panel .table th {
				width: 120px;
			}
			
			.panel .table tbody tr:first-child>* {
				border-top: 0px;
			}
			
			.panel .panel-body {
				padding: 5px 15px;
			}
			
			.panel-title .btn-group {
				float: right;
				padding-top: 2px;
				margin-top: -3px;
			}
			
			.panel-title .btn-group {
				float: right;
			}
			
			.panel-title .btn-xs {
				padding-top: 2px;
				margin-top: -3px;
			}
			
			.qrcbox {
				position: relative;
				width: 300px;
				height: 300px;
			}
			
			.qrcimg {
				margin: 0 auto;
				text-align: center;
			}
			
			.qrctxt {
				position: absolute;
				z-index: 2;
				left: 0px;
				top: 0px;
				right: 0px;
				bottom: 0px;
				text-align: center;
				vertical-align: middle;
				line-height: 280px;
			}
			
			.qrctxt>div {
				display: inline-block;
				background: rgb(64, 167, 231);
				color: #fff;
				font-size: 16px;
				padding: 10px;
				line-height: 20px;
			}
			
			.qrctxt a {
				text-decoration: none;
				color: #fff;
			}
			
			.filebtn {
				position: relative;
				display: inline-block;
				overflow: hidden;
			}
			
			.filebtn input {
				position: absolute;
				right: 0px;
				top: 0px;
				opacity: 0;
				-ms-filter: 'alpha(opacity=0)';
				font-size: 200px;
			}
			
			.table>tbody>tr>td,
			.table>tbody>tr>th,
			.table>tfoot>tr>td,
			.table>tfoot>tr>th,
			.table>thead>tr>td,
			.table>thead>tr>th {
				text-overflow: ellipsis;
				white-space: nowrap;
				overflow: hidden;
			}

			.imgbox {
				width: 150px;
				text-align: center;
			}

			.imgbox .upbtn {
				width: 100px;
				height: 100px;
				border: 1px solid #e2e2e2;
				cursor: pointer;
				position: relative;
				margin: 0 auto;
			}
			.imgbox .upbtn .uploadimg {
				width: 100%;
				height: 100%;
			}

			.change-img {
				width: auto;
				padding-top: 36px;
				padding-bottom: 12px;
			}

			.change-tip {
				font-size: 12px;
				color: #000;
				text-align: left;
			}

			.imgbox .upbtn input {
				opacity: 0;
				overflow: hidden;
				position: absolute;
				top: 0;
				bottom: 0;
				left: 0;
				right: 0;
				width: 100%;
				height: 100%;
				cursor: pointer;
			}
		</style>
	</head>

	<body>
		<div class="container-fluid">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h3 class="panel-title">企业基本信息</h3>
				</div>
				<div class="panel-body">
					<table class="table table-hover">
						<tbody>
							<tr>
								<th>企业名</th>
								<td>{$company.c0}</td>
							</tr>
							<tr>
								<th>企业标识</th>
								<td>{$company.c1}</td>
							</tr>
							<tr>
								<th>管理者</th>
								<td>{$company['c7'] ?: $company['c2']}</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
			<div class="panel panel-default">
				<div class="panel-heading">
					<h3 class="panel-title">
					ERP对接信息
					<div class="btn-group">
						<if condition="$company.c6 == 1 ">
						<button type="button" class="btn btn-default btn-xs" _w="1" title="修改配置">
							<span class="glyphicon glyphicon-cog"></span>
						</button>
						</if>
						<button type="button" class="btn btn-default btn-xs" _w="3" title="检测接口是否连通">
							<span class="glyphicon glyphicon-link"></span>
						</button>
					</div>
				</h3>
				</div>
				<div class="panel-body">
					<table class="table table-hover">
						<tbody>
							<tr>
								<th>静态IP / 域名</th>
								<td>{$company.d0}</td>
							</tr>
							<if condition="$company.c6 == 1 ">
							<tr>
								<th>端口（Port）</th>
								<td>{$company.d1}</td>
							</tr>
							<tr>
								<th>是否使用动态IP</th>
								<!--<td><empty name="company.d2"> 否 <else /> 是 </empty></td>-->
								<td>
									<if condition="$company.d2 eq 2 ">否
										<else /> 是
									</if>
								</td>
							</tr>
							<tr>
								<th>序列号</th>
								<td>{$company.d3}</td>
							</tr>
							<tr>
								<th>集团编号</th>
								<td>{$company.c3}</td>
							</tr>
							<tr>
								<th>公司编号</th>
								<td>{$company.c4}</td>
							</tr>
							<tr>
								<th>部门名称</th>
								<td>{$company.c5}</td>
							</tr>
							<tr>
								<th>服务品牌</th>
								<td>{$service_name}</td>
							</tr>
							<tr>
								<th>价格体系</th>
								<td>{$price_type_name}</td>
							</tr>
							</if>
						</tbody>
					</table>
				</div>
			</div>
			<div class="panel panel-default">
				<div class="panel-heading">
					<h3 class="panel-title">
					微信服务号对接信息
					<div class="btn-group">
						<button type="button" class="btn btn-default btn-xs" _w="99" title="授权第三方平台">
							<span class="glyphicon glyphicon-equalizer"></span>
						</button>
						<button type="button" class="btn btn-default btn-xs" _w="2" title="修改配置">
							<span class="glyphicon glyphicon-cog"></span>
						</button>
						<notempty name="company.e0">
						<button type="button" class="btn btn-default btn-xs" _w="4" title="商城二维码">
							<span class="glyphicon glyphicon-qrcode"></span>
						</button>
						</notempty>
					</div>
				</h3>
				</div>
				<div class="panel-body">
					<table class="table table-hover">
						<tbody>
							<tr>
								<th>wechatID</th>
								<td>{$company.e2}</td>
							</tr>
							<tr>
								<th>appID</th>
								<td>{$company.e0}</td>
							</tr>
							<tr>
								<th>appsecret</th>
								<td>{$company.e1}</td>
							</tr>
							<tr>
								<th>第三方授权</th>
								<td>
									<empty name="company.e7">
										<span style="color: red;">未授权</span>
									<else />
										<span style="color: green;">已授权</span>
									</empty>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
			<!-- 微信支付 -->
			<div class="panel panel-default">
				<div class="panel-heading">
					<h3 class="panel-title">
					微信支付配置信息
					<div class="btn-group">
						<button type="button" class="btn btn-default btn-xs" _w="9" title="修改配置">
							<span class="glyphicon glyphicon-cog"></span>
						</button>
					</div>
				</h3>
				</div>
				<div class="panel-body">
					<table class="table table-hover">
						<tbody>
							<tr>
								<th>商户ID</th>
								<td>{$company.e3}</td>
							</tr>
							<tr>
								<th>key</th>
								<td>{$company.e4}</td>
							</tr>
							<tr>
								<th>cert_path</th>
								<td>{$company.e5}</td>
							</tr>
							<tr>
								<th>key_path</th>
								<td>{$company.e6}</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
			<div class="panel panel-default">
				<div class="panel-heading">
					<h3 class="panel-title">
						小程序设置
						<div class="btn-group">
							<button type="button" class="btn btn-default btn-xs" _w="10" title="修改配置">
								<span class="glyphicon glyphicon-cog"></span>
							</button>
						</div>
					</h3>
				</div>
				<div class="panel-body">
					<table class="table table-hover">
						<tbody>
						<tr>
							<th>appID</th>
							<td>{$company.x0}</td>
						</tr>
						<tr>
							<th>appSecret</th>
							<td>{$company.x1}</td>
						</tr>
						<tr>
							<th>商户ID</th>
							<td>{$company.x2}</td>
						</tr>
						<tr>
							<th>支付key</th>
							<td>{$company.x3}</td>
						</tr>
						<tr>
							<th>certPath</th>
							<td>{$company.x4}</td>
						</tr>
						<tr>
							<th>keyPath</th>
							<td>{$company.x5}</td>
						</tr>
						<tr>
							<th>评价有效时间</th>
							<td>{$company.x6}天</td>
						</tr>
						<tr>
							<th>公司logo</th>
							<td>{$company.x7}</td>
						</tr>
						<tr>
							<th>护理通知延时</th>
							<td>{$company.x8}天</td>
						</tr>
						</tbody>
					</table>
				</div>
			</div>
			<if condition="$company.c6 == 1 ">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h3 class="panel-title">
					采宝对接信息
					<div class="btn-group">
						<button type="button" class="btn btn-default btn-xs" _w="5" title="修改配置">
							<span class="glyphicon glyphicon-cog"></span>
						</button>
						<notempty name="company.e0">
							<button type="button" class="btn btn-default btn-xs" _w="6" title="采宝后台">
								<a href="http://b.caibaopay.com/login/adminLogin.htm" style="color: black" target="_blank"><span class="glyphicon glyphicon-log-in"></span></a>
							</button>
						</notempty>
					</div>
				</h3>
				</div>
				<div class="panel-body">
					<table class="table table-hover">
						<tbody>
							<tr>
								<th>operator_id</th>
								<td>{$company.f0}</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
			</if>
			<if condition="$company.c8 == '0' ">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h3 class="panel-title">
					发票通对接信息
					<div class="btn-group">
						<button type="button" class="btn btn-default btn-xs" _w="8" title="增加配置">
							<span class="glyphicon glyphicon-plus"></span>
						</button>
						<notempty name="company.inv_cnf">
							<button type="button" class="btn btn-default btn-xs" title="发票通后台">
								<a href="https://www.fapiao.com/dzfp-web/index.do?method=index" style="color: black" target="_blank"><span class="glyphicon glyphicon-log-in"></span></a>
							</button>
						</notempty>
					</div>
				</h3>
				</div>
				<div class="panel-body">
					<table class="table table-hover">
						<tr>
							<th>配置名称</th>
							<th>appid</th>
							<th>操作</th>
						</tr>
						<notempty name="company.inv_cnf">
						<foreach name="company.inv_cnf" item="v">
						<tr>
							<td>{$v.g7}</td>
							<td>{$v.g0}</td>
							<td width="200">
								<button type="button" class="btn btn-default btn-xs" _w="8" _d="{$v.g8}" title="修改配置">
									<span class="glyphicon glyphicon-edit"></span>
								</button>
								<button type="button" class="btn btn-default btn-xs" _w="11" _d="{$v.g8}" title="删除配置">
									<span class="glyphicon glyphicon-remove"></span>
								</button>
							</td>
						</tr>
						</foreach>
						</notempty>
					</table>
				</div>
			</div>
			</if>
		</div>

		<div id="editErp" style="display: none;width: 48%;">
			<div>
				<div>
					<form class="form-horizontal">
						<div class="form-group">
							<label class="col-sm-3 control-label">静态IP / 域名</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_d0" cid="d0">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-3 control-label">端口（Port）</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_d1" cid="d1">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-3 control-label">使用动态IP</label>
							<div class="col-sm-8">
								<div class="checkbox">
									<label> <input type="checkbox" value="1" class="anchor_d2" cid="d2"> 启用
								</label>
								</div>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-3 control-label">序列号</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_d3" cid="d3">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-3 control-label">集团编号</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_c3" cid="c3">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-3 control-label">公司编号</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_c4" cid="c4">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-3 control-label">部门名称</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_c5" cid="c5">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-3 control-label">服务品牌</label>
							<div class="col-sm-8">
								<select name="service" class="form-control" cid="c9">
								</select>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-3 control-label">价格体系</label>
							<div class="col-sm-8">
								<select name="price_type_list" class="form-control" cid="c10">
								</select>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>

		<div id="editWx" style="display: none;">
			<div>
				<div>
					<form class="form-horizontal">
						<div class="form-group">
							<label class="col-sm-4 control-label">wechatID</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_e2" cid="e2">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-4 control-label">appID</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_e0" cid="e0">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-4 control-label">appsecret</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_e1" cid="e1">
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>

		<div id="editWxPay" style="display: none;">
			<div>
				<div>
					<form class="form-horizontal">
						<div class="form-group">
							<label class="col-sm-4 control-label">merchant_id</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_e2" cid="e3">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-4 control-label">key</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_e0" cid="e4">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-4 control-label">cert_path</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_e1" cid="e5" disabled>
								<span class="btn btn-primary btn-sm filebtn" t="e5" key="0">
					            <span>上传</span>
								<input type="file" name="ca">
								</span>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-4 control-label">key_path</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_e1" cid="e6" disabled>
								<span class="btn btn-primary btn-sm filebtn" t="e6" key="1">
					            <span>上传</span>
								<input type="file" name="ca">
								</span>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>

		<div id="editXcxPay" style="display: none;width:540px">
			<div>
				<div>
					<form class="form-horizontal">
						<div class="form-group">
							<label class="col-sm-4 control-label">appID</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_x0" cid="x0">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-4 control-label">appSecret</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_x1" cid="x1">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-4 control-label">merchantID</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_x2" cid="x2">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-4 control-label">key</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_x3" cid="x3">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-4 control-label">cert_path</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_x4" cid="x4" disabled>
								<span class="btn btn-primary btn-sm filebtn" t="x4" key="2">
					            <span>上传</span>
								<input type="file" name="ca">
								</span>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-4 control-label">key_path</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_x5" cid="x5" disabled>
								<span class="btn btn-primary btn-sm filebtn" t="x5" key="3">
					            <span>上传</span>
								<input type="file" name="ca">
								</span>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-4 control-label">评价有效时间(天)</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_x6" cid="x6">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-4 control-label">护理通知延时(天)</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_x8" cid="x8">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-4 control-label">公司logo</label>
							<div class="col-sm-8">
								<div class="imgbox">
									<div class="upbtn" style="margin-left:0px;">
										<img class="change-img anchor_x7" src="/ug/mall/assest/images/add-btn.png"  cid="x7">
										<input type="file" class="form-control" name="logo">
									</div>
									<div class="change-tip">点击上传图片</div>
								</div>
								<div class="change-tip">（图片小于25K 推荐50*60px）</div>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>

		<div id="editCb" style="display: none;">
			<div>
				<div>
					<form class="form-horizontal">
						<div class="form-group">
							<label class="col-sm-4 control-label">operator_id</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_e1" cid="f0">
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>
		<div id="viewQrc" style="display: none;">
			<div>
				<div class="qrcbox">
					<div class="qrcimg"></div>
					<div class="qrctxt">
						<div>
							<a href="{$url.mallQrc}" target="_blank">微信扫码</a>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div id="authQrc" style="display: none;">
			<div>
				<div class="qrcbox">
					<div class="qrcimg"></div>
					<div class="qrctxt">
						<div>
							<a href="{$url.mallQrc}" target="_blank">管理员扫码授权</a>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div id="editBw" style="display: none;">
			<div>
				<div>
					<form class="form-horizontal">
						<div class="form-group">
							<label class="col-sm-4 control-label">名称</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_g7" cid="g7">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-4 control-label">appid</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_g0" cid="g0">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-4 control-label">加密密码</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_g1" cid="g1">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-4 control-label">商品编码</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_g9" cid="g9">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-4 control-label">证书路径</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_g2" cid="g2" disabled>
								<span class="btn btn-primary btn-sm filebtn" t="g2" key="4">
					            <span>上传</span>
								<input type="file" name="ca">
								</span>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-4 control-label">证书密码</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_g3" cid="g3">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-4 control-label">纳税人识别号</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_g4" cid="g4">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-4 control-label">销售方名称</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_g5" cid="g5">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-4 control-label">销售方地址电话</label>
							<div class="col-sm-8">
								<input type="text" class="form-control anchor_g6" cid="g6">
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>

	</body>
	<script type="text/javascript" src="/ug/js/jquery.min.js"></script>
	<script type="text/javascript" src="/ug/js/jquery.qrcode.min.js"></script>
	<script type="text/javascript" src="/ug/js/plugincore.js"></script>
	<script type="text/javascript" src="/ug/admin/js/dialog.js"></script>
	<script type="text/javascript" src="/ug/admin/js/formvals.js"></script>
	<script type="text/javascript">
		var company = "#{$row}#";
		var services = "#{$services}#";
        var price_type_list = "#{$price_type_list}#";

		var wopt;
		$(function() {
			$('.btn-xs').on('click', function() {
				var inf = {
					"1": {
						dlg: $('#editErp'),
						url: '{$url.erp}',
					},
					"2": {
						dlg: $('#editWx'),
						url: '{$url.wx}',
					},
					"5": {
						dlg: $('#editCb'),
						url: '{$url.cb}',
					},
					"8": {
						dlg: $('#editBw'),
						url: '{$url.bw}',
					},
					"9": {
						dlg: $('#editWxPay'),
						url: '{$url.wx}'
					},
					"10": {
						dlg: $('#editXcxPay'),
						url: '{$url.xcx}'
					}
				};
				var w = $(this).attr('_w');
				wopt = w;
				if(w == '1')
				{
					initServices(company.c9);
                    initPriceType(company.c10);
				}
				if(w == '3') {
					testErp();
					return;
				}
				if(w == '4') {
					qrcode();
					return;
				}
				if (w == '8') {
					var turl = inf[w].url;
					var id = $(this).attr('_d') || '';
					if (id) {
						for (var i in company.inv_cnf) {
							if (id == company.inv_cnf[i].g8) {
								$.formvals().bindData(inf[w].dlg, 'cid', company.inv_cnf[i]);
							}
						}
					} else {
						$.formvals().bindData(inf[w].dlg, 'cid', '');
					}
					inf[w].dlg.dialog('修改信息', function() {
						var pdata = {};
						$.formvals($(this), 'cid', pdata);
						for (var k in pdata) {
							if (pdata[k] === '') {
								var anchor = $(this).find('.anchor_' + k);
								anchor.poptip('请输入信息');
								anchor.trigger('focus');
								return false;
							}
						}
						pdata.g8 = id;
						$.ajaxExt(turl, pdata, function(r) {
							if (r.isOk()) {
								window.location.reload();
							} else {
								$.alert(r.getMessage());
							}
						});
					});
					return;
				}
				if (w == '11') {
					var id = $(this).attr('_d') || '';
					if (id === '') {
						return false;
					}
					$.confirm('确定删除该配置吗？', function() {
						var purl = '{$url.bwdel}';
						$.ajaxExt(purl, {id: id}, function(r) {
							if (r.isOk()) {
								window.location.reload();
							} else {
								$.alert(r.getMessage());
							}
						});
					});
				}
				if (w == '99') {
					var purl = '{$url.preauthcode}';
					$.ajaxExt(purl, null, function(r) {
						if (r.isOk()) {
							authQrc(r.url);
						} else {
							$.alert(r.getMessage());
						}
					});
					return false;
				}
				if(inf[w]) {
					var turl = inf[w].url;
					$.formvals().bindData(inf[w].dlg, 'cid', company);
					if(w == 10){
						if(company.x7 !== '')
						{
							$ ('.change-tip').addClass ('none');
							$ ('.upbtn img').removeClass ('change-img').addClass ("uploadimg");
							$ ('.upbtn img').attr ('src',company.x7);
						}
					}
					inf[w].dlg.dialog('修改信息', function() {
						var pdata = {};
						$.formvals($(this), 'cid', pdata, null, {
							switcher: true
						});
						for(var k in pdata) {
							if(k == 'x7')
							{
								var src = $ ('.upbtn img').attr ('src');
								if(src != '/ug/mall/assest/images/add-btn.png')
								{
									pdata.x7 = src;
								}
							}
							if(pdata[k] === '' || pdata[k] === '0') {
								var ignoreArr = ['c9','x2','x3','x4','x5','x6','x8'];
								if(ignoreArr.indexOf(k)< 0)
								{
									var anchor = $(this).find('.anchor_' + k);
									anchor.poptip('请输入信息');
									anchor.trigger('focus');
									return false;
								}
							}
						}

						if(parseInt(pdata.d2) == 0) {
							pdata.d2 = 2;
						}

						$.ajaxExt(turl, pdata, function(jo) {
							if(jo.isOk()) {
								if(wopt == '1') {
									testErp(true);
								} else {
									window.location.reload();
								}
							} else {
								$.alert(jo.getMessage());
							}
						});
					});
				}
			});
		});

		function initServices (service_id) {
			var html = '<option value="">请选择</option>';
			for (var i = 0; i < services.length; i++) {
				if(service_id && service_id == services[i].id)
				{
					html += '<option value="' + services[i].id + '" selected>' + services[i].name + '</option>';
				}else{
					html += '<option value="' + services[i].id + '">' + services[i].name + '</option>';
				}
			}
			$ ("select[name=service]").html (html);
		}

        function initPriceType (price_type) {
            var html = '<option value="-1">请选择</option>';
            for (var i = 0; i < price_type_list.length; i++) {
                if(price_type && price_type == price_type_list[i].pricetype)
                {
                    html += '<option value="' + price_type_list[i].pricetype + '" selected>' + price_type_list[i].name + '</option>';
                }else{
                    html += '<option value="' + price_type_list[i].pricetype + '">' + price_type_list[i].name + '</option>';
                }
            }
            $ ("select[name=price_type_list]").html (html);
        }

		function testErp(rel) {
			var turl = "{$url.erptest}";
			$.ajaxExt(turl, {}, function(jo) {
				if(jo.isOk()) {
					if(rel) {
						window.location.reload();
					} else {
						$.alert('ERP连接成功');
					}
				} else {
					if(rel) {
						$.alert(jo.getMessage(), function() {
							window.location.reload();
						});
					} else {
						$.alert(jo.getMessage());
					}
				}
			});

		}

		function qrcode() {
			$('#viewQrc').find('.qrcimg').html('').qrcode({
				text: "{$url.mallQrc}",
				render: "canvas",
				width: 256,
				height: 256,
				typeNumber: -1,
				background: '#FFFFFF',
				foreground: '#000000',
			});
			$('#viewQrc').dialog('商城二维码').buttons();
		}

		function authQrc(url)
		{
			$('#authQrc').find('.qrcimg').html('').qrcode({
				text: url,
				render: "canvas",
				width: 256,
				height: 256,
				typeNumber: -1,
				background: '#FFFFFF',
				foreground: '#000000',
			});
			$('#authQrc').dialog('授权公众号').buttons();
		}

		$('.filebtn').change(function() {
			var type = $(this).attr('t');
			var key = $(this).attr('key');
			var formdata = new FormData();
			formdata.append('ca', $('input[name=ca]')[key].files[0]);
			$('input[name=ca]').val('');
			$.ajax({
				url: '{$url.upca}',
				method: 'POST',
				dataType: 'json',
				data: formdata,
				contentType: false,
				processData: false,
				cache: false,
				success: function(res) {
					if(res._c == 1) {
						$('[cid=' + type + ']').val(res.data);
					} else {
						$.alert(res._m);
					}
				}
			});
		});

		$ ('.upbtn').on ('change', function () {
			$ ('.change-tip').addClass ('none');
			$ ('.upbtn img').removeClass ('change-img').addClass ("uploadimg");
			var formData = new FormData ();
			formData.append ('file', $ ('[name=logo]')[0].files[0]);
			$.ajax ({
				url: '{$url.uploadLogo}',
				method: 'POST',
				data: formData,
				contentType: false,
				processData: false,
				cache: false,
				dataType: 'json',
				success: function (res) {
					if (res._c == 1) {
						if (res._m != '上传成功')
						{
							alert (res._m);
						}else{
							$ ('.upbtn img').attr ('src', res.data);
						}
					} else {
						alert (res._m);
					}
				}
			});
		});

	</script>

</html>