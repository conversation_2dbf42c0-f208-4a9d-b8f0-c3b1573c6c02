<block name="title">
<title>门店列表</title>
</block>
<block name="css">
	<!--<link rel="stylesheet" href="/ug/b/css/common.css">-->
	<link rel="stylesheet" href="/ug/b/css/default-skin.css">
	<link rel="stylesheet" href="/ug/b/css/modal-box.css">
    <link rel="stylesheet" href="/ug/admin/css/bootstrap.min.css">
    <!--<link rel="stylesheet" href="/ug/b/css/table.css">-->
	<link href="/manager/assets/css/analysis.css" rel="stylesheet">
	<style>
		.chart-title {
		    color: #555;
		    overflow: hidden;
		    text-overflow: ellipsis;
		    white-space: nowrap;
		    padding-bottom: 10px;
		    margin: 0;
		    font-weight: normal;
		    font-size: 16px;
		    text-align: center;
		}
		.tab,.tb_wrapper{
			padding: 10px 25px;
		}
		.table {
		    margin-bottom: 0px;
		    font-size: 12px;
		    border-collapse: collapse;
		}
		.table>tbody>tr>td, .table>tbody>tr>th, .table>tfoot>tr>td, .table>tfoot>tr>th, .table>thead>tr>td, .table>thead>tr>th {
		    padding: 8px;
		    line-height: 1.42857143;
		    vertical-align: middle;
		    border-top: 1px solid #ebeef5;
		}
		.table>tbody>tr>td, .table>tbody>tr>th, .table>tfoot>tr>td, .table>tfoot>tr>th, .table>thead>tr>td, .table>thead>tr>th {
		    border: 1px solid #ebeef5;
		}
		.table th {
		    color: #909399;
		}
		.paginate-wrap {
            text-align: right;
            border-top: 0;
            line-height: 1;
           padding: 10px 0;
            white-space: nowrap;
        }
        .paginate-left > a,
        .paginate-left > span > a,
        .button.btn-sure,
        .btn-sure {
            padding: 5px 4px;
            line-height: 1;
            border-radius: 4px;
            background-color: #FFF;
            color: rgb(51,50,50);
            font-size: 12px;
            margin: 0 4px;
            min-width: 14px;
            float: left;
            height: 13px;
            text-align: center;
        }
        .paginate-wrap a {
            cursor: pointer;
        }
        .omit {
            color: rgb(51,50,50);
            font-style: normal;
        }
        .paginate-left > a,
        .paginate-left > span > a,
        .paginate-right > span > input,
        .btn-sure {
            border: 1px solid rgb(178,177,177);
            position: relative;
        }
        .paginate-left,
        .paginate-right {
            display: inline-block;
            vertical-align: middle;
        }
        .paginate-right > span {
            color: rgb(51,50,50);
            font-size: 12px;
            margin-right: 8px;
            float: left;
            line-height: 24px;
            height: 24px;
        }
        .paginate-right > span > input {
            min-width: 22px;
            width: 22px;
            border-radius: 4px;
            padding: 0;
            height: 22px;
            line-height: 22px;
            font-size: 12px;
            text-align: center;
            margin: 0 8px;
        }
        .paginate-right > span > input, .btn-sure {
            vertical-align: bottom;
        }
        .paginate_button.active,
        .paginate-wrap a.active,
        .paginate-wrap a:hover {
            color: #fff!important;
            background-color: #4F88AD;
            border-color: #4F88AD;
        }
        .dt-btn-event{
                color: #606266;
                padding: 5px 10px;
            font-size: 12px;
            line-height: 1.5;
            border-radius: 3px;
                border: 1px solid #dcdfe6;
        }
        em {
            font-style: normal;
        }
        a{
            text-decoration: none;
        }
        .tab a:hover,.tab a.active {
            text-decoration: none;
        }
	</style>
</block>

<block name="js">
	<script type="text/javascript" src="/ug/js/jquery.min.js"></script>
	<script type="text/javascript" src="/ug/js/plugincore.js"></script>
	<script type="text/javascript" src="/ug/b/js/dialog.js"></script>
	<script type="text/javascript" src="/ug/b/js/datatable.js"></script>
	<script src="https://cdn.bootcss.com/echarts/3.7.2/echarts.min.js"></script>
    <script type="text/javascript" src="/manager/laydate/laydate.js"></script>
	<script type="text/javascript">
		var timeBOxArr = format_category();
		$(function() {
		    var tbDate = {rows: [], pagi: {_pn: 1, _tot: 0, _lmt: 15}};
		    tbDate.url = '{$url.user}';

		    tbDate.cols = [{
				col: 'branch_name',
				tit: '连锁门店名称',
				rep: function(d) {
				    if(!d.branch_name) {
				        return d.business_name;
				    } else {
				        return d.branch_name;
				    }
				},
			}, {
				col: 'pr_one_cnt',
				tit: timeBOxArr.shift() + '增长总数',
			}, {
				col: 'pr_two_cnt',
				tit: timeBOxArr.shift() + '增长总数',
			}, {
				col: 'pr_three_cnt',
				tit: timeBOxArr.shift() + '增长总数',
			}, {
				col: 'pr_four_cnt',
				tit: timeBOxArr.shift() + '增长总数',
			}, {
				col: 'pr_five_cnt',
				tit: timeBOxArr.shift() + '增长总数',
			}, {
				col: 'pr_six_cnt',
				tit: timeBOxArr.shift() + '增长总数',
			}, {
				col: 'tot_cnt',
				tit: '用户总数',
			}];

		    tbDate.onLoad = function(json) {
		        var r = format_chart_data(json.rows);
		        var param = {};
//		        param.text = '近6个月粉丝增长变化';
		        param.shop = r[0];
		        param.category = format_category();
		        param.lists = r[1];
		        init_chart(param);
		    };
		    $('#tblcont').datatable(tbDate).load();

            var now = new Date();
            var now_year = now.getFullYear();

            laydate.render({
                elem: '[name=choose_year]',
                type:'year',
                value:now_year,
                max:"now_year"
            });

		    $('.tab a').click(function() {
		        $(this).addClass('active').siblings('a').removeClass('active');
		        var eq = $(this).index();
		        $('.tab-div').eq(eq).show().siblings('div').hide();
		        if (eq == 1) {
		            init_act_tb();
		        }
		    });
		    
		    $("#shop_select").click(function () {
                var pdata = {};
                var year = $("#choose_year").val();
                pdata.year = year;
                var tbl = {
                    url: '{$url.userByYear}',
                    rows: [
                    ],
                    cols: [{
                            col: 'branch_name',
                            tit: '连锁门店名称',
                            rep: function(d) {
                                if(!d.branch_name) {
                                    return d.business_name;
                                } else {
                                    return d.branch_name;
                                }
                            }
                        }, {
                            col: 'pr_1_cnt',
                            tit: '1月增长总数'
                        }, {
                            col: 'pr_2_cnt',
                            tit: '2月增长总数'
                        },{
                            col: 'pr_3_cnt',
                            tit: '3月增长总数'
                        },{
                            col: 'pr_4_cnt',
                            tit: '4月增长总数'
                        },{
                            col: 'pr_5_cnt',
                            tit: '5月增长总数'
                        },{
                            col: 'pr_6_cnt',
                            tit: '6月增长总数'
                        },{
                            col: 'pr_7_cnt',
                            tit: '7月增长总数'
                        },{
                            col: 'pr_8_cnt',
                            tit: '8月增长总数'
                        },{
                            col: 'pr_9_cnt',
                            tit:'9月增长总数'
                        },{
                            col: 'pr_10_cnt',
                            tit: '10月增长总数'
                        },{
                            col: 'pr_11_cnt',
                            tit: '11月增长总数'
                        },{
                            col: 'pr_12_cnt',
                            tit: '12月增长总数'
                        }
                    ],
                    pagi: {
                        _pn: 0,
                        _lmt: 10,
                        _tot: 0
                    },
                    query: {
                        filter: function() {
                            return pdata;
                        },
                        btn:'push'
                    }
                };

                $('#shop_cnt').datatable(tbl).load();
            })

            $("#shop_export").click(function () {
                var choose_year = $("#choose_year").val();
                window.location.href = "{$url.exportUserByYear}" + '?year=' + choose_year;
            })
		});

		function format_category() {
		    var timeBoxArr = [];
		    var n = 6;

		    for (var i = n-1; i >= 0; i--) {
		    	var dt = new Date();
		    	dt.setMonth(dt.getMonth()-i);
		    	var y = dt.getFullYear();
			    var m = dt.getMonth()+1;
		        var d = y + '年' + m + '月';
		        timeBoxArr.push(d);
		    }
		    return timeBoxArr;
		}

		function format_chart_data(rawData) {
		    var shop_name_arr = [];
		    var lists = [];
		    var o = {};
		    for(var i=0;i<rawData.length;i++){
		        var shop_name = rawData[i].branch_name || rawData[i].business_name;
		        shop_name_arr.push(shop_name);
		        o.name = shop_name;
		        o.type = 'line';
		        o.markPoint = {
		        	data: [{
		        		type: 'max',
		        		name: '最大值'
		        	}, {
		        		type: 'min',
		        		name: '最小值'
		        	}
		        ]};
		        o.markLine = {
		        	data: [{
		        		type: 'average',
		        		name: '平均值'
		        	}
		        ]};
		        o.data = format_single_data(rawData[i]);
		        lists.push(o);
		        o = {};
		    }
		    return [shop_name_arr,lists];
		}

		function format_single_data(data) {
		    var res = [];
		    for(var i in data) {
		        if(!data.hasOwnProperty(i)) {
		            continue;
		        }
		        if(i.indexOf('pr')!=-1){
		            res.push(data[i]);
		        }
		    }
		    return res;
		}
		
		function init_chart(param) {
		    var myChart = echarts.init(document.getElementById('analysis'));
		    var option = {
		        title: {
		            text: param.text
		        },
		        tooltip: {
		            trigger: 'axis'
		        },
		        legend: {
		            data:param.shop
		        },
		        grid: {
		            left: '3%',
		            right: '4%',
		            bottom: '3%',
		            containLabel: true
		        },
		        toolbox: {
		            feature: {
		                saveAsImage: {}
		            }
		        },
		        xAxis: {
		            type: 'category',
		            boundaryGap: false,
		            data: param.category
		        },
		        yAxis: {
		            type: 'value'
		        },
		        series: param.lists
		    };
		    myChart.setOption(option);
		}

		function init_act_tb() {
		    var tb = {rows: [], pagi: {_pn: 1, _tot: 0, _lmt: 15}};
		    tb.url = '{$url.act}';

		    tb.cols = [{
		        col: 'title',
		        tit: '活动标题'
		    }, {
		        col: 'coupon_count',
		        tit: '券种类数',
		    }, {
		        col: 'type_str',
		        tit: '活动有效时间',
		    }, {
		        col: 'created_at',
		        tit: '发布日期',
		    }];

		    tb.menus = {
		        callback : function(tag, d) {
		            switch(tag){
		                case 'sis':
		                    sis(d, $(this));
		                    break;
		                default :
		                    break;
		            }
		        },
		        rows : [{
		            tag: 'sis',
		            title: '统计',
		            icon: 'glyphicon glyphicon-send'
		        }]
		    };
		    $('#acttb').datatable(tb).load();
		}

		function sis(data, obj) {
		    $.ajaxExt('{$url.actcoupon}', {ids: data.coupon_id}, 
		        function(res) {
		            if (res.isOk()) {
		                load_table(res.rows)
		            } else {
		                $.alert(res.getMessage());
		            }
		    })
		}

		function load_table(data) {
		    var htm = '<table class="table"><tr><th>优惠券名称</th><th>券的有效时间</th><th>参与活动的门店名称</th><th>使用次数</th><th>总领取人数</th><th>使用转化率</th></tr>';
			if (data.length == 0) {
				htm += '<tr><td colspan="6" style="text-align: center;">无</td></tr></table>';
				$('#sistb').html(htm);
				return;
			}
		    var c1 = 0;
		    var c2 = 0;
		    var c3 = 0;
		    for (var i=0; i<data.length; i++) {
		        htm += '<tr><td rowspan="' + data[i].stores.length + '">' + data[i].classname + '</td>';
		        htm += '<td rowspan="' + data[i].stores.length + '">' + data[i].period + '</td>';
		        c2 += parseInt(data[i].get_number);
		        for (var j=0; j<data[i].stores.length; j++) {
		            htm += '<td>'
		                + data[i].stores[j].branch_name
		                + '</td>'
		                + '<td>'
		                + data[i].stores[j].use_number
		                + '</td>'
		                + '<td>'
		                + data[i].get_number
		                + '</td>'
		                + '<td>'
		                + data[i].stores[j].percent + '%'
		                + '</td>'
		                + '</tr>';

		            c1 += parseInt(data[i].stores[j].use_number);
		            c3 += parseFloat(data[i].stores[j].percent);
		        }
		    }
		    htm += '<tr>'
		            + '<td colspan="3">总计'
		            + '</td>'
		            + '<td>'
		            + c1
		            + '</td>'
		            + '<td>'
		            + c2
		            + '</td>'
		            + '<td>'
		            + c3 + '%'
		            + '</td>'
		            + '</tr>';
		    htm += '</table>';
		    $('#sistb').html(htm);
		}
	</script>
</block>

<block name="free_area">
    <div class="tab">
        <a href="javascript:void(0);" class="active">门店用户增长统计</a><a href="javascript:void(0);">活动门店统计</a><a href="javascript:void(0);">门店用户增长导出</a>
    </div>

    <div class="cont">

        <!-- 用户增长统计 -->
        <div class="tab-div user-tab">
            <div class="content-header">
            	<h4 class="chart-title">近6个月的增长变化</h4>
                <div id="analysis" style="height:400px;"></div>
            </div>
            <div id="tblcont" class="tb_wrapper"></div>
        </div>
        <!-- 活动门店统计 -->
        <div class="tab-div activity-tab" style="display: none;">
            <div id="acttb"></div>
            <div id="sistb"></div>
        </div>
        <!-- 用户增长导出 -->
        <div class="tab-div shop-tab" style="display: none;padding: 10px 25px;">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">门店用户增长统计报表</h3>
                </div>
                <div class="panel-body">
                    筛选年份：<input type="text" name="choose_year" id="choose_year" class="form-control" style="width: 100px;display: inline-block;"> 年
                    <div style="float: right;">
                        <a href="javascript:void(0);" id="shop_select" class="btn btn-primary submit"><span class="glyphicon glyphicon-search"></span> 查询</a>
                        <a href="javascript:void(0);" id="shop_export" class="btn btn-default submit"><span class="glyphicon glyphicon-export"></span> 导出</a>
                    </div>
                </div>
            </div>
            <div class="content">
                <div id="shop_cnt" class="tb_wrapper"></div>
            </div>
        </div>
    </div>
</block>