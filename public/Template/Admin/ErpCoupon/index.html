<extend name="Base@Common/listtable" />
<block name="title">
<title>优惠券列表</title>
</block>
<block name="js"> <script type="text/javascript">

	inf_dtbl.cols = [{
		col: 'c1',
		tit: '券名称'
	}, {
		col: 'c0',
		tit: '优惠券类别'
	}, {
		col : 'c10',
		sortable : true,
		tit : '优惠券类型',
		rep : function(d) {
			var str = '';
			if (d.c10 == 1) {
				str = '线下优惠券';
			} else if (d.c10 == 2) {
				str = '线上优惠券';
			} else if (d.c10 == 3) {
				str = '通用优惠券';
			}
			return str;
		}
	},  {
		col : 'c13',
		sortable : true,
		tit : '线上使用范围',
		rep : function(d) {
			var str = '';
			if (d.c13 == 1 && d.c10 != 1) {
				str = '全部商品';
			} else if (d.c13 == 2 && d.c10 != 1) {
				str = '指定商品'+'</br><button onclick="showGoods(this,\''+ d.c1+'\')" data-cid="'+ d.id+'" class="td-row-btn" style="padding:2px">查看绑定</button>';
			} else {}
			return str;
		}
	}, {
		col : 'c12',
		tit : '优惠',
		rep : function(d) {
			if (d.c0 == '折扣券' && d.c14 == 1) {
				return "满"+ parseInt(d.c11) +'打'+ d.c2+'折';
			}
			if (d.c0 == '折扣券' && d.c14 == 2) {
				return "满"+parseInt(d.c11)+'减'+parseInt(d.c2);
			}
			return "满"+parseInt(d.c11)+'减'+parseInt(d.c2);
		}
	}, {
		tit: '有效期',
		rep: function(d) {
			if (d.c3 == 0) {
				return d.c4.date('yyyy-MM-dd') + '至' + d.c5.date('yyyy-MM-dd');
			} else if (d.c3 == 1) {
				return '领券' + d.c6 + '天后生效，' + d.c7 + '天后失效';
			} else {}
		}
	}, {
		col: 'c8',
		tit: '发放数量'
	}, {
		col: 'c12',
		tit: '剩余'
	}];
	
	inf_dtbl.menus.dynamic = function(d) {
		var menus = [];
		if (d.c9 == 0) {
			menus.push({
				tag: 'release',
				title: '上架',
				icon: 'glyphicon glyphicon-circle-arrow-up'
			});
			menus.push({
				tag: 'edit',
				title: '编辑',
				icon: 'glyphicon glyphicon-edit'
			});
		} else if (d.c9 == 2) {
			menus.push({
				tag: 'release',
				title: '下架',
				icon: 'glyphicon glyphicon-circle-arrow-down'
			});
			menus.push({
				tag: 'issuedetail',
				title: '详情',
				icon: 'glyphicon glyphicon-list'
			});
		}
		if (d.c9 == 0) {
			menus.push({
				tag: 'del',
				title: '删除',
				icon: 'glyphicon glyphicon-trash'
			});
		}

		return menus;
	};
	
	inf_ext.menus.callback = function(tag, d){
		switch(tag){
			case 'release':
				release(d, $(this));
				break;
			case 'edit':
				$.openTab('{$url.edit}' + '?id=' + d.id, '编辑优惠券');
				break;
			case 'issuedetail':
				$.openTab('{$url.detail}' + '?id=' + d.id, '优惠券发放详情');
				break;
			default :
				break;
		}
	};

	function release(data, obj)
	{
		$.confirm('确定进行操作吗？', function() {
			var turl = '{$url.release}';
			$.ajaxExt(turl, {id: data.id}, function(jo) {
				if (jo.isOk()) {
					window.location.reload();
				} else {
					$.alert(jo.getMessage());
				}
			});
		});
	}

	function showGoods(obj,name){
		button = $(obj)
		var cid = button.data('cid')
		var url =  '{$url.goods}';
		$.openTab(url+"?coup_id="+cid,name);
	}

	$(function() {
		$("#btn_add").on('click', function(){
			$.openTab('{$url.add}', '新增优惠券');
		})
	})
</script> 
</block>

<block name="query_ui">
	<div class="form-group">
		<select class="form-control" cid="q{$k++}">
			<option value="0">优惠券类别</option>
			<option value="1">代价券</option>
			<option value="2">折扣券</option>
		</select>
		<select class="form-control" cid="q{$k++}">
			<option value="0">优惠券类型</option>
			<option value="1">线下优惠券</option>
			<option value="2">线上优惠券</option>
			<option value="3">通用优惠券</option>
		</select>
		<input type="text" class="form-control" cid="q{$k++}" placeholder="搜优惠券名称">
	</div>
</block>
<block name="tool_box">
	<button id="btn_add" type="button" class="btn btn-sm btn-default admin-btn-tab">
		<span class="glyphicon glyphicon-plus"></span>&nbsp;新增
	</button>
</block>