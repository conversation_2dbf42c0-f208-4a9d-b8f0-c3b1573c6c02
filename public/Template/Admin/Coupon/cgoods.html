<extend name="Base@Common/listtable" />
<block name="title">
    <title>商品列表</title>
</block>
<block name="js"> <script type="text/javascript">
    inf_dtbl.cols = [ {
        col : 'c1',
        tit : '商品名称'
    },{
        col : 'c2',
        tit : '商品状态',
        rep : function(d) {
//            1:未设置 2:已设置 3:有商品上架 4:无商品上架
            var str = '';
            if (d.c2 == 1) {
                str = '<span style="">未设置</span>';
            } else if (d.c2 == 2) {
                str = '<span style="">已设置</span>';
            } else if(d.c2 ==3) {
                str = '<span style="">有商品上架</span>'
            } else if(d.c2==4){
                str = '<span style="">无商品上架</span>';
            }
            return str;
        }
    }];

    inf_dtbl.menus.rows.push({
        tag : 'delGoods',
        title : '解除绑定',
        icon : 'glyphicon glyphicon-trash'
    });
    inf_ext.menus.callback = function(tag, d){
        switch(tag){
            case 'delGoods':
                delGoods(d.c0);
                break;
            default :
                break;
        }
    };

    function delGoods(id){
        var pdata ={};
        var turl = '{$url.delgoods}';
        pdata.gid = id
        pdata.cid ='{$cid}';
        $.ajaxExt(turl, pdata, function(jo) {
            if (jo.isOk()) {
                window.location.reload();
            } else {
                $.alert(jo.getMessage());
            }
        });
    }

</script> </block>