<extend name="Base@Common/listtable"/>
<block name="css">
    <style type="text/css">
        .dialog-box {
            width: 780px;
        }
    </style>
</block>
<block name="title">
    <title>优惠券列表</title>
</block>
<block name="js">
    <script src="/manager/laydate/laydate.js"></script>
    <script type="text/javascript">

        inf_dtbl.cols = [{
            col: 'c0',
            tit: '优惠券名称'
        }, {
            col: 'c2',
            sortable: true,
            tit: '使用范围',
            rep: function (d) {
                var str = '';
                if (d.c2 == 1) {
                    str = '全部商品';
                } else if (d.c2 == 2) {
                    str = '指定商品' + '</br><button onclick="showGoods(this,\'' + d.c0 + '\')" data-cid="' + d.id + '" class="td-row-btn" style="padding:2px">查看绑定</button>';
                } else {
                }
                return str;
            }
        }, {
            col: 'c3',
            tit: '满减优惠',
            rep: function (d) {
                return "满" + parseInt(d.c3) + '减' + parseInt(d.c4);
            }
        },
//		{
//		col : 'c5',
//		sortable : true,
//		tit : '领取上限',
//		rep : function(d) {
//			return parseInt(d.c5);
//		}
//	},
            {
                col: 'c6',
                sortable: true,
                tit: '库存',
                rep: function (d) {
                    return parseInt(d.c6);
                }
            }, {
                col: 'c7',
                tit: '发放时间',
                rep: function (d) {
                    return d.c7.date('yyyy-MM-dd') + '--' + d.c8.date('yyyy-MM-dd');
                }
            }, {
                col: 'c8',
                tit: '有效时间',
                rep: function (d) {
                    return d.c9.date('yyyy-MM-dd') + '--' + d.c10.date('yyyy-MM-dd');
                }
            }, {
                col: 'c11',
                sortable: true,
                tit: '状态',
                rep: function (d) {
                    var str = '';
                    if (d.c11 == 0) {
                        str = '<span style="color:red;">已下架</span>';
                    } else if (d.c11 == 1) {
                        str = '<span style="color:green;">已上架</span>';
                    } else {
                    }
                    return str;
                }
            }];
        inf_dtbl.menus.dynamic = function (d) {
            var menus = [];
            if (d.c11 == '1') {
                menus.push({
                    tag: 'toState',
                    title: '下架',
                    icon: 'glyphicon glyphicon-arrow-down'
                });
            } else {
                menus.push({
                    tag: 'toState',
                    title: '上架',
                    icon: 'glyphicon glyphicon-arrow-up'
                });
//			menus.push({
//				tag : 'del',
//				title : '删除',
//				icon : 'glyphicon glyphicon-trash'
//			});
                menus.push({
                    tag: 'editCoupon',
                    title: '编辑',
                    icon: 'glyphicon glyphicon-edit'
                });
                menus.push({
                    tag: 'selfDel',
                    title: '删除',
                    icon: 'glyphicon glyphicon-trash'
                });

            }
            return menus;
        };

        inf_ext.menus.callback = function (tag, d) {
            switch (tag) {
                case 'toState':
                    stateSave(d, $(this));
                    break;
                case 'selfDel':
                    $("#selfDelDlg").show();
                    var jq = $(this);
                    $("#selfDelDlgBtnOk").on('click', function () {
                        delCoupon(d.id, jq)
                    });
                    break;
                case 'editCoupon':
                    $('#editdlg').find('input').val('');
                    $('#couponid').val(d.id);
                    $('#name').val(d.c0);
                    $('#tips').val(d.c15);
                    $('#des').val(d.c16);
                    $(".edit_class_id option[value='" + d.c12 + "']").attr("selected", true);
                    var class_id = d.c12;
                    var turl = '/admin/coupon/couponBrand';
                    var pdata = {action: 'get_brand', class_id: class_id};
                    // $.ajaxExt(turl, pdata, function(jo) {
                    // 	if (jo.isOk()) {
                    // 		var opt = '';
                    // 		for(var i =0; i<jo.data.length; i++) {
                    // 			opt += '<option value="' + jo.data[i].brandid + '">' + jo.data[i].name + '</option>';
                    // 		}
                    // 		$('.edit_brand_id option:not(:first)').remove();
                    // 		$('.edit_breed_id').empty().append('<option value="-1">请选择</option>');
                    // 		$('.edit_brand_id').append(opt);
                    // 		$(".edit_brand_id option[value='"+d.c13+"']").attr("selected", true);
                    // 	}else {
                    // 		$.alert(jo.getMessage());
                    // 	}
                    // });
                    // var brand_id = d.c13;
                    // var turl = '/admin/coupon/couponBrand';
                    // var pdata = {action: 'get_breed',brand_id: brand_id};
                    // $.ajaxExt(turl, pdata, function(jo) {
                    // 	if (jo.isOk()) {
                    // 		var opt = '';
                    // 		for(var i =0; i<jo.data.length; i++) {
                    // 			opt += '<option value="' + jo.data[i].classid + '">' + jo.data[i].name + '</option>';
                    // 		}
                    // 		$('.edit_breed_id option:not(:first)').remove();
                    // 		$('.edit_breed_id').append(opt);
                    // 		$(".edit_breed_id option[value='"+d.c14+"']").attr("selected", true);
                    // 	}else {
                    // 		$.alert(jo.getMessage());
                    // 	}
                    // });
                    $("input:radio[name='edit_range']").eq(parseInt(d.c2) - 1).attr("checked", true);
                    $("input:radio[name='edit_range']").eq(0).attr("value", 1);
                    $("input:radio[name='edit_range']").eq(1).attr("value", 2);
                    $('#total').val(parseInt(d.c3));
                    $('#minus').val(parseInt(d.c4));
                    $('#limit').val(parseInt(d.c5));
                    $('#stock').val(parseInt(d.c6));
                    $('#edit_receive_stime').val(d.c7.date('yyyy-MM-dd'));
                    $('#edit_receive_etime').val(d.c8.date('yyyy-MM-dd'));
                    $('#edit_use_stime').val(d.c9.date('yyyy-MM-dd'));
                    $('#edit_use_etime').val(d.c10.date('yyyy-MM-dd'));
                    $('#editdlg').dialog('编辑优惠券', editSave);
                    break;
                default :
                    break;
            }
        };

        function showGoods(obj, name) {
            button = $(obj)
            var cid = button.data('cid')
            $.openTab("/admin/coupon/cgoods?cid=" + cid, name);
        }

        $(function () {
            $('#btn_add').on('click', function () {
                $('#adddlg').dialog('新增优惠券', addSave);
            });

//		初始化时间控件
            initTimePicker();

//		初始化品牌
            var turl = '/admin/coupon/couponBrand';
            var pdata = {action: 'get_class'};
            $.ajaxExt(turl, pdata, function (jo) {
                if (jo.isOk()) {
                    var opt = '';
                    for (var i = 0; i < jo.data.length; i++) {
                        opt += '<option value="' + jo.data[i].classid + '">' + jo.data[i].name + '</option>';
                    }
                    $('.class_id option:not(:first)').remove();
                    $('.class_id').append(opt);
                    $('.edit_class_id option:not(:first)').remove();
                    $('.edit_class_id').append(opt);
                }
            });

//		列表点击事件
            $(".class_id").on("change", function () {
                var class_id = $(this).val();
                var turl = '/admin/coupon/couponBrand';
                var pdata = {action: 'get_brand', class_id: class_id};
                $.ajaxExt(turl, pdata, function (jo) {
                    if (jo.isOk()) {
                        var opt = '';
                        for (var i = 0; i < jo.data.length; i++) {
                            opt += '<option value="' + jo.data[i].brandid + '">' + jo.data[i].name + '</option>';
                        }
                        $('.brand_id option:not(:first)').remove();
                        $('.breed_id').empty().append('<option value="-1">请选择</option>');
                        $('.brand_id').append(opt);
                    } else {
                        $.alert(jo.getMessage());
                    }
                });
            });
            $(".brand_id").on('change', function () {
                var brand_id = $(this).val();
                var turl = '/admin/coupon/couponBrand';
                var pdata = {action: 'get_breed', brand_id: brand_id};
                $.ajaxExt(turl, pdata, function (jo) {
                    if (jo.isOk()) {
                        var opt = '';
                        for (var i = 0; i < jo.data.length; i++) {
                            opt += '<option value="' + jo.data[i].classid + '">' + jo.data[i].name + '</option>';
                        }
                        $('.breed_id option:not(:first)').remove();
                        $('.breed_id').append(opt);
                    } else {
                        $.alert(jo.getMessage());
                    }
                });
            });
            $(".edit_class_id").on("change", function () {
                var class_id = $(this).val();
                var turl = '/admin/coupon/couponBrand';
                var pdata = {action: 'get_brand', class_id: class_id};
                $.ajaxExt(turl, pdata, function (jo) {
                    if (jo.isOk()) {
                        var opt = '';
                        for (var i = 0; i < jo.data.length; i++) {
                            opt += '<option value="' + jo.data[i].brandid + '">' + jo.data[i].name + '</option>';
                        }
                        $('.edit_brand_id option:not(:first)').remove();
                        $('.edit_breed_id').empty().append('<option value="-1">请选择</option>');
                        $('.edit_brand_id').append(opt);
                    } else {
                        $.alert(jo.getMessage());
                    }
                });
            });
            $(".edit_brand_id").on('change', function () {
                var brand_id = $(this).val();
                var turl = '/admin/coupon/couponBrand';
                var pdata = {action: 'get_breed', brand_id: brand_id};
                $.ajaxExt(turl, pdata, function (jo) {
                    if (jo.isOk()) {
                        var opt = '';
                        for (var i = 0; i < jo.data.length; i++) {
                            opt += '<option value="' + jo.data[i].classid + '">' + jo.data[i].name + '</option>';
                        }
                        $('.edit_breed_id option:not(:first)').remove();
                        $('.edit_breed_id').append(opt);
                    } else {
                        $.alert(jo.getMessage());
                    }
                });
            });

            //自定义对话框事件绑定
            $("#selfDelDlgBtnNo,#selfDelDlgBtnClose").on('click', function () {
                $("#selfDelDlg").hide();
            })
        });

        function editSave() {
            var pdata = {};
            $.formvals($(this), 'cid', pdata);
            for (var k in pdata) {
                if (k == 'c20' || k == 'c3' || k=='c5') continue;
                if (pdata[k] === '' || pdata[k] === '0') {
                    var anchor = $(this).find('.anchor_' + k);
                    anchor.poptip('请输入或选择信息');
                    anchor.trigger('focus');
                    return false;
                }
                var errMsg = checkParam(k, pdata[k]);
                if (errMsg != "") {
                    var anchor = $(this).find('.anchor_' + k);
                    anchor.poptip(errMsg);
                    anchor.trigger('focus');
                    return false;
                }
            }
            var turl = '{$url.editsave}';
            $.ajaxExt(turl, pdata, function (jo) {
                if (jo.isOk()) {
                    window.location.reload();
                } else {
                    $.alert(jo.getMessage());
                }
            });
        }

        function stateSave(d) {
            var pdata = {
                id: d.id,
                c0: (d.c11 == '1' ? 0 : 1)
            };
            var turl = '{$url.statesave}';
            $.ajaxExt(turl, pdata, function (jo) {
                if (jo.isOk()) {
//				if(d.c1 == '1'){
//					tpl_obj.tblctl.poptip('下架成功');
//				}else{
//					tpl_obj.tblctl.poptip('上架成功');
//				}
                    tpl_obj.tblctl.load(false);
                } else {
                    $.alert(jo.getMessage());
                }
            });
        }

        function addSave() {
            var pdata = {};
            $.formvals($(this), 'cid', pdata);
            console.log(pdata);
            for (var k in pdata) {
                if (k == 'c20' || k == 'c3' || k=='c5') continue;
                if (pdata[k] === '' || pdata[k] === '0') {
                    var anchor = $(this).find('.anchor_' + k);
                    anchor.poptip('请输入或选择信息');
                    anchor.trigger('focus');
                    return false;
                }
                var errMsg = checkParam(k, pdata[k]);
                if (errMsg != "") {
                    var anchor = $(this).find('.anchor_' + k);
                    anchor.poptip(errMsg);
                    anchor.trigger('focus');
                    return false;
                }
            }
            var turl = '{$url.addsave}';
            $.ajaxExt(turl, pdata, function (jo) {
                if (jo.isOk()) {
                    window.location.reload();
                } else {
                    $.alert(jo.getMessage());
                }
            });
        }

        function delCoupon(id, jq) {
            if (!id) {
                return;
            }
            var pdata = {
                id: id
            };
            var turl = '/Admin/Coupon/couponDel';
            $.ajaxExt(turl, pdata, function (jo) {
                if (jo.isOk()) {
                    $("#selfDelDlg").hide();
                    tpl_obj.evtjq = jq;
                    tpl_obj.evttr = jq.parents('*[_no]:first');
                    tpl_obj.evttr.remove();
                    inf_callback.del();

//				window.location.reload();
                } else {
                    $.alert(jo.getMessage());
                }
            });

        }

        function checkParam(key, val) {
            var errMsg = '';
            switch (key) {
                case 'c3':
                case 'c4':
                case 'c5':
                case 'c6':
                    var reg = /^[1-9]\d*$/;
                    if (!reg.test(val)) {
                        errMsg = "请填写正确数值";
                    }
                    break;
            }
            return errMsg;
        }

        function initTimePicker() {
            var myDate = new Date();
            var year = myDate.getFullYear();
            var month = myDate.getMonth() + 1;
            var date = myDate.getDate();
            var now = year + '-' + month + '-' + date;
            var receive_start = {
                elem: '#receive_stime',
                type: 'date',
                min: now,
                show: true,
                closeStop: '#receive_stime'

            };
            var receive_end = {
                elem: '#receive_etime',
                type: 'date',
                show: true,
                closeStop: '#receive_etime'
            };
            lay('#receive_stime').on('click', function (e) {
                if ($('#receive_etime').val() != null && $('#receive_etime').val() != undefined && $('#receive_etime').val() != '') {
                    receive_start.max = $('#receive_etime').val();
                }
                laydate.render(receive_start);
            });
            lay('#receive_etime').on('click', function (e) {
                if ($('#receive_stime').val() != null && $('#receive_stime').val() != undefined && $('#receive_stime').val() != '') {
                    receive_end.min = $('#receive_stime').val();
                }
                laydate.render(receive_end);
            });

            var use_start = {
                elem: '#use_stime',
                type: 'date',
                min: now,
                show: true,
                closeStop: '#use_stime'

            };
            var use_end = {
                elem: '#use_etime',
                type: 'date',
                show: true,
                closeStop: '#use_etime'
            };
            lay('#use_stime').on('click', function (e) {
                if ($('#use_etime').val() != null && $('#use_etime').val() != undefined && $('#use_etime').val() != '') {
                    use_start.max = $('#use_etime').val();
                }
                laydate.render(use_start);
            });
            lay('#use_etime').on('click', function (e) {
                if ($('#use_stime').val() != null && $('#use_stime').val() != undefined && $('#use_stime').val() != '') {
                    use_end.min = $('#use_stime').val();
                }
                laydate.render(use_end);
            });

            var edit_receive_start = {
                elem: '#edit_receive_stime',
                type: 'date',
                min: now,
                show: true,
                closeStop: '#edit_receive_stime'

            };
            var edit_receive_end = {
                elem: '#edit_receive_etime',
                type: 'date',
                show: true,
                closeStop: '#edit_receive_etime'
            };
            lay('#edit_receive_stime').on('click', function (e) {
                if ($('#edit_receive_etime').val() != null && $('#edit_receive_etime').val() != undefined && $('#edit_receive_etime').val() != '') {
                    edit_receive_start.max = $('#edit_receive_etime').val();
                }
                laydate.render(edit_receive_start);
            });
            lay('#edit_receive_etime').on('click', function (e) {
                if ($('#edit_receive_stime').val() != null && $('#edit_receive_stime').val() != undefined && $('#edit_receive_stime').val() != '') {
                    edit_receive_end.min = $('#edit_receive_stime').val();
                }
                laydate.render(edit_receive_end);
            });

            var edit_use_start = {
                elem: '#edit_use_stime',
                type: 'date',
                min: now,
                show: true,
                closeStop: '#edit_use_stime'

            };
            var edit_use_end = {
                elem: '#edit_use_etime',
                type: 'date',
                show: true,
                closeStop: '#edit_use_etime'
            };
            lay('#edit_use_stime').on('click', function (e) {
                if ($('#edit_use_etime').val() != null && $('#edit_use_etime').val() != undefined && $('#edit_use_etime').val() != '') {
                    edit_use_start.max = $('#edit_use_etime').val();
                }
                laydate.render(edit_use_start);
            });
            lay('#edit_use_etime').on('click', function (e) {
                if ($('#edit_use_stime').val() != null && $('#edit_use_stime').val() != undefined && $('#edit_use_stime').val() != '') {
                    edit_use_end.min = $('#edit_use_stime').val();
                }
                laydate.render(edit_use_end);
            });
        }
    </script>
</block>

<block name="query_ui">
    <div class="form-group">
        <!--<label>优惠券名称</label> --><input type="text" class="form-control input-sm" cid="q{$k++}" placeholder="搜优惠券名称">
    </div>
</block>
<block name="tool_box">
    <button id="btn_add" type="button" class="btn btn-sm btn-default admin-btn-tab">
        <span class="glyphicon glyphicon-plus"></span>&nbsp;新增
    </button>
</block>
<block name="free_area">
    <div id="adddlg" style="display: none;">
        <div>
            <div>
                <form class="form-horizontal">
                    <div class="form-group">
                        <label class="col-sm-3 control-label">优惠券名称:</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control anchor_c0" cid="c0">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">tips:</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control anchor_c20" cid="c20">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">优惠券描述:</label>
                        <div class="col-sm-6">
							<textarea class="form-control anchor_c21" cid="c21">
							</textarea>
                        </div>
                    </div>
                    <!--					<div class="form-group">-->
                    <!--						<label class="col-sm-3 control-label">优惠券类型:</label>-->
                    <!--						<div class="col-sm-6 input-group anchor_c3">-->
                    <!--							<div class="col-sm-4">-->
                    <!--								<select class="form-control class_id" cid="c12">-->
                    <!--									<option value="-1">请选择</option>-->
                    <!--								</select>-->
                    <!--							</div>-->
                    <!--							<div class="col-sm-4">-->
                    <!--								<select class="form-control brand_id" cid="c13">-->
                    <!--									<option value="-1">请选择</option>-->
                    <!--								</select>-->
                    <!--							</div>-->
                    <!--							<div class="col-sm-4">-->
                    <!--								<select class="form-control breed_id" cid="c14">-->
                    <!--									<option value="-1">请选择</option>-->
                    <!--								</select>-->
                    <!--							</div>-->
                    <!--						</div>-->
                    <!--					</div>-->
                    <div class="form-group">
                        <label class="col-sm-3 control-label">使用范围:</label>
                        <div class="col-sm-4">
                            <label class="radio-inline">
                                <input type="radio" class="anchor_c2" name="range" cid="c2" value="1" checked>全部商品可用
                            </label>
                            <label class="radio-inline">
                                <input type="radio" class="anchor_c2" name="range" cid="c2" value="2">指定商品可用
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">优惠信息:</label>
                        <div class="form-inline col-sm-9">
                            <div class="form-group col-sm-12">
                                满&nbsp; <input type="text" class="form-control anchor_c3" cid="c3"> 元，优惠
                                <input type="text" class="form-control anchor_c4" cid="c4"> 元
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
						<label class="col-sm-3 control-label">领取上限:</label>
						<div class="col-sm-2">
							<input type="text" class="form-control anchor_c5" cid="c5">
						</div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">库存:</label>
                        <div class="col-sm-2">
                            <input type="text" class="form-control anchor_c6" cid="c6">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">发放时间:</label>
                        <div class="form-inline col-sm-9">
                            <div class="form-group col-sm-12">
                                <input type="text" class="form-control anchor_c7" id="receive_stime" cid="c7">
                                 -
                                <input type="email" class="form-control anchor_c8" id="receive_etime" cid="c8">
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">有效时间:</label>
                        <div class="form-inline col-sm-9">
                            <div class="form-group col-sm-12">
                                <input type="text" class="form-control anchor_c9" id="use_stime" cid="c9">  -
                                <input type="email" class="form-control anchor_c10" id="use_etime" cid="c10">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div id="editdlg" style="display: none;">
        <div>
            <div>
                <form class="form-horizontal">
                    <div class="form-group">
                        <label class="col-sm-3 control-label">优惠券名称:</label>
                        <div class="col-sm-6">
                            <input id="couponid" type="hidden" cid="id">
                            <input type="text" class="form-control anchor_c0" cid="c0" id="name">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">tips:</label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control anchor_c20" cid="c20" id="tips">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">优惠券描述:</label>
                        <div class="col-sm-6">
							<textarea class="form-control anchor_c21" cid="c21" id="des">
							</textarea>
                        </div>
                    </div>
                    <!--					<div class="form-group">-->
                    <!--						<label class="col-sm-3 control-label">优惠券类型:</label>-->
                    <!--						<div class="col-sm-6 input-group anchor_c3">-->
                    <!--							<div class="col-sm-4">-->
                    <!--								<select class="form-control edit_class_id" cid="c12">-->
                    <!--									<option value="-1">请选择</option>-->
                    <!--								</select>-->
                    <!--							</div>-->
                    <!--							<div class="col-sm-4">-->
                    <!--								<select class="form-control edit_brand_id" cid="c13">-->
                    <!--									<option value="-1">请选择</option>-->
                    <!--								</select>-->
                    <!--							</div>-->
                    <!--							<div class="col-sm-4">-->
                    <!--								<select class="form-control edit_breed_id" cid="c14">-->
                    <!--									<option value="-1">请选择</option>-->
                    <!--								</select>-->
                    <!--							</div>-->
                    <!--						</div>-->
                    <!--					</div>-->
                    <div class="form-group">
                        <label class="col-sm-3 control-label">使用范围:</label>
                        <div class="col-sm-4">
                            <label class="radio-inline">
                                <input type="radio" class="anchor_c2" name="edit_range" cid="c2" value="1">全部商品可用
                            </label>
                            <label class="radio-inline">
                                <input type="radio" class="anchor_c2" name="edit_range" cid="c2" value="2">指定商品可用
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">优惠信息:</label>
                        <div class="form-inline col-sm-9">
                            <div class="form-group col-sm-12">
                                满&nbsp; <input type="text" class="form-control anchor_c3" cid="c3" id="total"> 元，优惠
                                <input type="text" class="form-control anchor_c4" cid="c4" id="minus"> 元
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
						<label class="col-sm-3 control-label">领取上限:</label>
						<div class="col-sm-2">
							<input type="text" class="form-control anchor_c5" cid="c5" id="limit">
						</div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">库存:</label>
                        <div class="col-sm-2">
                            <input type="text" class="form-control anchor_c6" cid="c6" id="stock">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">发放时间:</label>
                        <div class="form-inline col-sm-9">
                            <div class="form-group col-sm-12">
                                <input type="text" class="form-control anchor_c7" id="edit_receive_stime" cid="c7">
                                 -
                                <input type="email" class="form-control anchor_c8" id="edit_receive_etime" cid="c8">
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">有效时间:</label>
                        <div class="form-inline col-sm-9">
                            <div class="form-group col-sm-12">
                                <input type="text" class="form-control anchor_c9" id="edit_use_stime" cid="c9">
                                 -
                                <input type="email" class="form-control anchor_c10" id="edit_use_etime" cid="c10">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!--自定义删除对话框-->
    <div id="selfDelDlg" style="display: none">
        <div class="modal-backdrop fade in" style=""></div>
        <div class="dialog-box" style="top: 234px; left: 475.5px; right: auto; bottom: auto;">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" id="selfDelDlgBtnClose"><span>×</span></button>
                    <h4 class="modal-title"><i class="glyphicon glyphicon-warning-sign"></i>提示</h4>
                </div>
                <div class="modal-body">删除该优惠券商品绑定的优惠券也会同时删除,你确定删除吗？</div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" id="selfDelDlgBtnNo">取消</button>
                    <button type="button" class="btn btn-primary" id="selfDelDlgBtnOk">确认</button>
                </div>
            </div>
        </div>
    </div>
</block>
