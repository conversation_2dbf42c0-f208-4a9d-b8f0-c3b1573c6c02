<!DOCTYPE html>
<html>
<head>
	<meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no" />
	<title>配置检查工具</title>
	<link rel="stylesheet" href="/ug/admin/css/bootstrap.min.css">
	<link rel="stylesheet" href="/ug/admin/css/tabpage.css">
	<link rel="stylesheet" href="/ug/admin/css/dialog.css">
	<style type="text/css">
		@-webkit-keyframes rotation{
			from {
				-webkit-transform: rotate(0deg);
			}
			to {
				-webkit-transform: rotate(360deg);
			}
		}
		.glyphicon.glyphicon-refresh {
			-webkit-transform: rotate(360deg);
			animation: rotation 1s linear infinite;
			-moz-animation: rotation 1s linear infinite;
			-webkit-animation: rotation 1s linear infinite;
			-o-animation: rotation 1s linear infinite;
		}
		.loading {
			text-align: center;
			padding: 50px 0;
		}
	</style>
</head>
<body>
	<div class="container">
		<table class="table table-bordered">
			<tr>
				<td width="80">企业名</td>
				<td>{$company.name}</td>
			</tr>
			<tr>
				<td>企业标识</td>
				<td>{$company.cno}</td>
			</tr>
		</table>
		<div class="table-result" style="display: none;">
			<table class="table table-bordered">
				<tbody>
					<tr>
						<th>ERP配置</th>
						<td align="right">
							<span class="erpstat"></span>
						</td>
					</tr>
					<tr>
						<th>微信配置</th>
						<td align="right">
							<span class="wxstat"></span>
						</td>
					</tr>
					<tr>
						<th>采宝配置</th>
						<td align="right">
							<span class="cbstat"></span>
						</td>
					</tr>
					<tr>
						<th>发票通配置</th>
						<td align="right">
							<span class="bwstat"></span>
						</td>
					</tr>
				</tbody>
			</table>
			<a href="javascript:;" class="btn btn-primary sendtopc">发送电脑处理</a>
		</div>
		<div class="loading"><i class="glyphicon glyphicon-refresh"></i></div>
	</div>
<script type="text/javascript" src="/ug/js/jquery.min.js"></script>
<script type="text/javascript" src="/ug/js/plugincore.js"></script>
<script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.2.0.js"></script>
<script type="text/javascript">
	var ret;
	$(function() {
		$.ajax({
			url: '{$url.checkall}',
			type: 'post',
			dataType: 'json',
			data: {
				id: '{$company.id}'
			}
		}).done(function(r) {
			if (r._c == 0) {
				ret = r.data;
				getlocation();
				resultHandle(ret);
			}
		}).fail(function(e) {
			console.log(e);
		});

		$('.sendtopc').click(function() {
			$.post('{$url.sendtopc}', {
				cd: '{$cd}',
				data: ret
			}, function(r) {
				console.log(r);
			}, 'json');
		});
	})

	function resultHandle(data) {
		var strs = {
			erp: {
				s: ['【ERP未配置】', '【ERP已配置】'],
				conn: ['【ERP连接失败】', '【ERP连接成功】']
			},
			wx: {
				s: ['【公众号未配置】', '【公众号已配置】'],
				wxpay: ['【微信支付未配置】', '【微信支付已配置】'],
				mp: ['【配置错误】', '【配置正确】'],
				js: ['【JSSDK功能异常】', '【JSSDK功能正常】'],
				paytest: ['【微信支付异常】', '【微信支付正常】']
			},
			cb: {
				s: ['【采宝未配置】', '【采宝已配置】'],
				cbtest: ['【采宝支付异常】', '【采宝支付正常】']
			},
			bw: {
				s: ['【发票通未配置】', '【发票通已配置】'],
				bwtest: ['【发票通连接异常】', '【发票通连接正常】']
			}
		};
		for (var t in data) {
			var htm = '';
			for (var s in data[t]) {
				var key = data[t][s];
				var val = strs[t][s][key];
				var style = '';
				if (key == 0) {
					style = 'style="color: red"';
				} else if (key == 1) {
					style = 'style="color: green"';
				} else {}
				htm += '<font ' + style + '>' + val + '</font>';
			}
			$('.' + t + 'stat').html(htm);
		}
		$('.loading').hide();
		$('.table-result').show();
	}

	function getlocation() {
		var jscnf = '{$jscnf}';
		if (jscnf == '') {
			ret.wx.js = 0;
			return;
		}
		wx.config(jscnf);
		wx.error(function(e) {
			ret.wx.js = 0;
			var newstr = '<font style="color: red;">【JSSDK功能异常】</font>';
			var oldstr = $('.wxstat').html();
			var newstr = oldstr.replace(/<font style="color: green">【JSSDK功能正常】<\/font>/, newstr);
			$('.wxstat').html(newstr);
		});
	}
</script>
</body>
</html>