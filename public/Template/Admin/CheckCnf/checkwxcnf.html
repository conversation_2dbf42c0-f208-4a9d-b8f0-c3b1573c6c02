<!DOCTYPE html>
<html>
<head>
	<meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no" />
	<title>微信配置检查</title>
	<link rel="stylesheet" href="/ug/admin/css/bootstrap.min.css">
	<link rel="stylesheet" href="/ug/admin/css/tabpage.css">
	<link rel="stylesheet" href="/ug/admin/css/dialog.css">
	<style type="text/css">
		@-webkit-keyframes rotation{
			from {
				-webkit-transform: rotate(0deg);
			}
			to {
				-webkit-transform: rotate(360deg);
			}
		}
		.glyphicon.glyphicon-refresh {
			-webkit-transform: rotate(360deg);
			animation: rotation 1s linear infinite;
			-moz-animation: rotation 1s linear infinite;
			-webkit-animation: rotation 1s linear infinite;
			-o-animation: rotation 1s linear infinite;
		}
		.loading {
			text-align: center;
			padding: 50px 0;
		}
	</style>
</head>
<body>
	<div class="container">
		<div class="loading"><i class="glyphicon glyphicon-refresh"></i></div>
	</div>
<script type="text/javascript" src="/ug/js/jquery.min.js"></script>
<script type="text/javascript" src="/ug/js/plugincore.js"></script>
<script type="text/javascript" src="https://res.wx.qq.com/open/js/jweixin-1.2.0.js"></script>
<script type="text/javascript">
	var jscnf = "#{$jscnf}#";
	var wxret = "#{$wxret}#";
	var wxcode = '{$wxcode}';
	$(function() {
		if (wxcode) {
			window.location.replace(wxcode);
		}
		if (wxret.wxset == 1) {
			getlocation(wxret);
		} else {
			send(wxret);
		}
	})

	function getlocation(ret) {
		if (jscnf == '') {
			ret.js = 0;
			send(ret);
		}
		wx.config(jscnf);
		wx.error(function(e) {
			ret.js = 0;
			send(ret);
		});
		wx.ready(function() {
			wx.getLocation({
				type: 'wgs84',
				success: function (res) {
					ret.js = 1;
				},
				fail: function (r) {
					ret.js = 0;
				},
				complete: function (r) {
					send(ret);
				}
			});
		});
	}

	function send(ret) {
		$.post('{$url.sendto}', {
			cd: '{$cd}',
			data: ret
		}, function(r) {
			if (r._c == 0) {
				$('.loading').html('<h3>检测完成，请在电脑查看</h3>');
			} else {
				$('.loading').html('<h3>发生错误</h3>');
			}
		}, 'json');
	}
</script>
</body>
</html>