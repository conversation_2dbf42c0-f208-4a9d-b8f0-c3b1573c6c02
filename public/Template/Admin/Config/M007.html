<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"/>
    <block name="title">
        <title>汉高微信后台管理</title>
    </block>
    <link rel="stylesheet" href="/ug/admin/css/bootstrap.min.css">
    <link rel="stylesheet" href="/ug/admin/css/tabpage.css">
    <link rel="stylesheet" href="/ug/admin/css/dialog.css">
    <link rel="stylesheet" href="/ug/b/css/add-coupons.css">
    <style type="text/css">
        #store-sel ul {
            list-style: none;
        }

        .img-view img {
            width: 200px;
            height: auto;
        }

        #coupon-form input {
            border-radius: 4px;
            padding: 6px 12px;
            border: 1px solid #dcdfe6;
        }

        .div-box-item .form-control {
            width: 70%;
        }
    </style>
</head>
<body>
<div class="content-add-body">
    <form class="form-horizontal" id="coupon-form">
        <div class="form-group">
            <label class="col-sm-2 control-label">积分体系：</label>
            <div class="col-sm-6">
                <textarea id="editor_wrap" name="detail" placeholder="积分体系" style="width: 400px; min-height: 100px;"></textarea>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label"></label>
            <div class="col-sm-3">
                <a href="javascript:void(0);" class="btn btn-default-bg save">保存</a>
            </div>
        </div>
    </form>
</div>
</body>
<script type="text/javascript" src="/ug/js/jquery.min.js"></script>
<script type="text/javascript" src="/ug/js/plugincore.js"></script>
<script type="text/javascript" src="/ug/admin/js/dialog.js"></script>
<script type="text/javascript" src="/ug/admin/js/formvals.js"></script>

<block name="js">
<script type="text/javascript">
    var url = "#{$url}#";
    var data = "#{$data}#";

    $ (function () {
        $('[name=detail]').val(data.des);
    })

    $ ('.save').click (function () {
        var content = $('#editor_wrap').val();
        if (! content || $.trim(content) == '') {
            $.alert ('请填写内容');
            return;
        }
        var pdata = {
            mid: data.mid,
            des: content
        };
        $.ajaxExt (url.do, pdata, function (jo) {
            if (jo.isOk ()) {
                $.alert ('保存成功', function () {
                    window.location.reload ();
                });
            } else {
                $.alert (jo.getMessage ());
            }
        });
    });
</script>
</block>
</html>