<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"/>
    <title>汉高微信后台管理</title>
    <link rel="stylesheet" href="/ug/admin/css/bootstrap.min.css">
    <link rel="stylesheet" href="/ug/admin/css/dialog.css">
    <link rel="stylesheet" href="https://cdn.staticfile.org/twitter-bootstrap/3.3.7/css/bootstrap.min.css">
    <style type="text/css">

        .table-width-style{
            width: 960px;
        }

        .link-option {
            cursor: pointer
        }

        .links-zone {
            height: 300px;
            overflow: auto;
        }

        #query_ui {
            padding: 5px;
        }

        .select-zone-container {
            display: flex;
            display: -webkit-flex;
            flex-direction: row;
            flex-wrap: nowrap;
            justify-content: space-between;
            align-items: center;
        }

        .img {
            width: 100px;
            height: 60px;
        }

        .input-width {
            width: 150px;
        }

        .spa-page-wrap {
            display: none;
        }

        .query-box {
            padding-bottom: 10px;
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
            align-items: center;
        }

        .table > tbody > tr > td,
        .table > tbody > tr > th,
        .table > tfoot > tr > td,
        .table > tfoot > tr > th,
        .table > thead > tr > td,
        .table > thead > tr > th {
            vertical-align: middle;
        }

        .table-bordered > thead > tr > td,
        .table-bordered > thead > tr > th {
            border-width: 1px;
        }

        .form-control {
            box-shadow: none;
            -webkit-box-shadow: none;
        }

        td .form-inline select,
        td .form-inline span {
            margin-left: -1px;
            border-radius: 0 3px 3px 0;
        }

        .form-inline .form-control:focus {
            margin-right: 1px;
        }

        .btn-default {
            border-color: rgb(220, 223, 230);
        }

        .btn-default:hover,
        .btn-default:focus {
            background-color: rgb(79, 136, 173);
            border-color: rgb(79, 136, 173);
            color: #fff;
        }

        .btn-save {
            color: #fff;
            background-color: rgb(79, 136, 173);
        }

        .op > .btn:not(:first-child) {
            border-radius: 0 3px 3px 0;
            margin-left: -1px;
        }

        .admin-btn-default {
            background-color: #ffa83b;
            border-color: #ffa83b;
            color: #fff;
        }

        .admin-btn-default:hover {
            background-color: #e89021;
            border-color: #e89021;
            color: #fff;
        }

        .form-control:focus {
            outline: none;
            border: 1px solid #dcdfe6;
            box-shadow: none;
            -webkit-box-shadow: none;
        }

        .defind-width {
            min-width: 960px;
        }

        .td1{
            width: 10%;
        }

        .td2{
            width: 20%;
        }

        .td3{
            width: 20%;
        }

        .td4{
            width: 45%;
        }

        .td5{
            width: 5%;
        }
    </style>
</head>

<body>
<div class="container-fluid defined-width">
    <div class="row">
        <form method="post" id="slidedata" enctype="multipart/form-data">
            <div class="table-width-style">
                <div class="table-responsive" id="slide">
                    <table class="table table-striped table-bordered table-hover">
                        <thead>
                        <tr class="info">
                            <td colspan="5" align="center">
                                <h4>轮播图<small>（推荐分辨率 500 * 330）</small></h4>
                            </td>
                        </tr>
                        <tr>
                            <th class="td1">上传</th>
                            <th class="td2">图片</th>
                            <th class="td3">标题</th>
                            <th class="td4">链接</th>
                            <th name="op"  class="td5"><span class="btn btn-default btn-sm" name="add">添加</span></th>
                        </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                    <span class="btn btn-default-bg btn-save" name="save" style="margin-top: -8px;">保存</span>
                </div>
            </div>
        </form>
    </div>
    <hr class="table-width-style">
    <div class="row">
        <form method="post" id="entrancedata" enctype="multipart/form-data">
            <div class="table-width-style">
                <div class="table-responsive" id="entrance">
                    <table class="table table-striped table-bordered table-hover">
                        <thead>
                        <tr class="info">
                            <td colspan="5" align="center">
                                <h4>快捷入口<small>（推荐分辨率 45 * 45）</small></h4>
                            </td>
                        </tr>
                        <tr>
                            <th class="td1">上传</th>
                            <th class="td2">图片</th>
                            <th class="td3">标题</th>
                            <th class="td4">链接</th>
                            <th class="td5" name="op"><span class="btn btn-default btn-sm" name="add">添加</span></th>
                        </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                    <span class="btn btn-default-bg btn-save" name="save" style="margin-top: -8px;">保存</span>
                </div>
            </div>
        </form>
    </div>
    <hr class="table-width-style">
    <div class="row">
        <form method="post" id="model1data" enctype="multipart/form-data">
            <div class="table-width-style">
                <h4 class="sub-header">
                    <div class="form-group"><label
                            style="float:left;margin-top: 5px;">自定义模块名称:&nbsp;&nbsp;&nbsp;</label><input type="text"
                                                                                                         class="form-control"
                                                                                                         style="width: 200px"
                                                                                                         name="config_title"
                                                                                                         value=""></div>
                </h4>
                <div class="table-responsive" id="model1">
                    <table class="table table-striped table-bordered table-hover">
                        <thead>
                        <tr class="info">
                            <th class="td1">上传</th>
                            <th class="td2">图片</th>
                            <th>标题</th>
                            <th>副标题</th>
                            <th style="width: 50%">链接</th>
                            <th name="op"  class="td5"><span class="btn btn-default btn-sm" name="add">添加</span></th>
                        </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                    <span class="btn btn-default-bg btn-save" name="save" style="margin-top: -8px;">保存</span>
                </div>
            </div>
        </form>
    </div>
    <hr class="table-width-style">
    <div class="row">
        <form method="post" id="model2data" enctype="multipart/form-data">
            <div class="table-width-style">
                <h4 class="sub-header">
                    <div class="form-group"><label
                            style="float:left;margin-top: 5px;">自定义模块名称:&nbsp;&nbsp;&nbsp;</label><input type="text"
                                                                                                         class="form-control"
                                                                                                         style="width: 200px"
                                                                                                         name="config_title"
                                                                                                         value=""></div>
                </h4>
                <div class="table-responsive" id="model2">
                    <table class="table table-striped table-bordered table-hover">
                        <thead>
                        <tr class="info">
                            <th style="width: 45%">商品</th>
                            <th style="width: 45%">标签(多个标签用逗号分隔)</th>
                            <th style="width: 10%" name="op"><span class="btn btn-default btn-sm" name="add">选择</span></th>
                        </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                    <span class="btn btn-default-bg btn-save" name="save" style="margin-top: -8px;">保存</span>
                </div>
            </div>
        </form>
    </div>
</div>
<!-- 模态框（Modal） -->
<div class="modal fade" id="selectLinkModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
     aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                    &times;
                </button>

                <div class="select-operate">
                    <div id="query_ui" class="form-inline query-box">
                        <div>

                            <div class="form-group query-group">
                                <input type="text" class="form-control input" id="link-name" placeholder="名称">
                            </div>

                            <div class="form-group query-group select-link-type">
                            </div>

                            <button id="link-query" type="button" class="btn btn-default">
                                <span class="glyphicon glyphicon-search"></span>&nbsp;查询
                            </button>

                        </div>
                    </div>
                </div>
            </div>

            <ul class="list-group links-zone">
            </ul>
        </div><!-- /.modal-content -->
    </div><!-- /.modal -->
</div>
</body>
<script type="text/javascript" src="/ug/js/jquery.min.js"></script>
<script type="text/javascript" src="/ug/js/plugincore.js"></script>
<script type="text/javascript" src="/ug/admin/js/dialog.js"></script>
<script type="text/javascript" src="/ug/admin/js/jsSPA.js"></script>
<script type="text/javascript" src="/ug/admin/plupload.full.min.js"></script>
<script type="text/javascript" src="/ug/admin/qiniu4js.min.js"></script>
<script type="text/javascript" src="/manager/js/clipBoard.min.js"></script>
<script src="https://cdn.staticfile.org/twitter-bootstrap/3.3.7/js/bootstrap.min.js"></script>
<script type="text/javascript">
    var imgInfo = {
        slide: {
            width: 500,
            height: 333,
        },
        entrance: {
            width: 45,
            height: 45,
        },
        model1: {
            width: 500,
            height: 333,
        },
    };
    var page = 1;
    var tot = 0;
    var url = "#{$url}#";
    var data = "#{$data}#";
    var slideHtml = '<tr _no="###">\
        <td><input type="hidden" name="photo" value="">\
        <sapn id="###" class="browse_button btn btn-default btn-sm">选择</sapn></td>\
        <td><img class="img" src="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9InllcyI/PjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMTQwIiBoZWlnaHQ9IjE0MCIgdmlld0JveD0iMCAwIDE0MCAxNDAiIHByZXNlcnZlQXNwZWN0UmF0aW89Im5vbmUiPjwhLS0KU291cmNlIFVSTDogaG9sZGVyLmpzLzE0MHgxNDAKQ3JlYXRlZCB3aXRoIEhvbGRlci5qcyAyLjYuMC4KTGVhcm4gbW9yZSBhdCBodHRwOi8vaG9sZGVyanMuY29tCihjKSAyMDEyLTIwMTUgSXZhbiBNYWxvcGluc2t5IC0gaHR0cDovL2ltc2t5LmNvCi0tPjxkZWZzPjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+PCFbQ0RBVEFbI2hvbGRlcl8xNjBiNWRmNmRjNyB0ZXh0IHsgZmlsbDojQUFBQUFBO2ZvbnQtd2VpZ2h0OmJvbGQ7Zm9udC1mYW1pbHk6QXJpYWwsIEhlbHZldGljYSwgT3BlbiBTYW5zLCBzYW5zLXNlcmlmLCBtb25vc3BhY2U7Zm9udC1zaXplOjEwcHQgfSBdXT48L3N0eWxlPjwvZGVmcz48ZyBpZD0iaG9sZGVyXzE2MGI1ZGY2ZGM3Ij48cmVjdCB3aWR0aD0iMTQwIiBoZWlnaHQ9IjE0MCIgZmlsbD0iI0VFRUVFRSIvPjxnPjx0ZXh0IHg9IjQ0LjA2MjUiIHk9Ijc0LjM1OTM3NSI+MTQweDE0MDwvdGV4dD48L2c+PC9nPjwvc3ZnPg=="/></td>\
        <td><input type="text" name="title" class="form-control input-sm input-width" value=""></td>\
        <td><form class="form-inline">\
        <div class="form-group"><input type="text" name="url" class="form-control input-sm input-width" value=""></div>\
        <div class="form-group">' + getSelect() + '</form></td>\
        <td name="op">\
        <span class="btn btn-default btn-sm" name="del">删除</span>\
        </td>\
        </tr>';
    var modelHtml = '<tr _no="###">\
        <td><input type="hidden" name="photo" value="">\
        <sapn id="###" class="browse_button btn btn-default btn-sm">选择</sapn></td>\
        <td><img class="img" src="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9InllcyI/PjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMTQwIiBoZWlnaHQ9IjE0MCIgdmlld0JveD0iMCAwIDE0MCAxNDAiIHByZXNlcnZlQXNwZWN0UmF0aW89Im5vbmUiPjwhLS0KU291cmNlIFVSTDogaG9sZGVyLmpzLzE0MHgxNDAKQ3JlYXRlZCB3aXRoIEhvbGRlci5qcyAyLjYuMC4KTGVhcm4gbW9yZSBhdCBodHRwOi8vaG9sZGVyanMuY29tCihjKSAyMDEyLTIwMTUgSXZhbiBNYWxvcGluc2t5IC0gaHR0cDovL2ltc2t5LmNvCi0tPjxkZWZzPjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+PCFbQ0RBVEFbI2hvbGRlcl8xNjBiNWRmNmRjNyB0ZXh0IHsgZmlsbDojQUFBQUFBO2ZvbnQtd2VpZ2h0OmJvbGQ7Zm9udC1mYW1pbHk6QXJpYWwsIEhlbHZldGljYSwgT3BlbiBTYW5zLCBzYW5zLXNlcmlmLCBtb25vc3BhY2U7Zm9udC1zaXplOjEwcHQgfSBdXT48L3N0eWxlPjwvZGVmcz48ZyBpZD0iaG9sZGVyXzE2MGI1ZGY2ZGM3Ij48cmVjdCB3aWR0aD0iMTQwIiBoZWlnaHQ9IjE0MCIgZmlsbD0iI0VFRUVFRSIvPjxnPjx0ZXh0IHg9IjQ0LjA2MjUiIHk9Ijc0LjM1OTM3NSI+MTQweDE0MDwvdGV4dD48L2c+PC9nPjwvc3ZnPg=="/></td>\
        <td><input type="text" name="title" class="form-control input-sm input-width" value=""></td>\
        <td><input type="text" name="sub_title" class="form-control input-sm input-width" value=""></td>\
        <td><fom class="form-inline">\
        <div class="form-group"><input type="text" name="url" class="form-control input-sm input-width" value=""></div>\
        <div class="form-group">' + getSelect() + '</div></td>\
        </div></td>\
        <td name="op">\
        <span class="btn btn-default btn-sm" name="del">删除</span>\
        </td>\
        </tr>';

    var linksData = null;

    function renderLinks() {
        var pdata = {};
        var turl = '/admin/Config/getUrlList';
        $.ajaxExt(turl, pdata, function (jo) {
            if (jo.isOk()) {
                var data = jo.data;
                linksData = data;
                if (data.length < 1) {
                    return;
                }
                renderLinkType(data);
                renderLinkOption(data.slice(0, 1));
            }
        });
    }

    function renderLinkType(data) {
        var html = '<select class="form-control" id="link-type-zone">';
        for (var i = 0; i < data.length; i++) {
            html += '<option value="' + data[i].type + '">' + data[i].name + '</option>';
        }
        html += '</select>';
        $('.select-link-type').html(html);
    }

    function renderLinkOption(data) {
        var html = '';
        for (var i = 0; i < data.length; i++) {
            var links = data[i].list;
            var type = data[i].type;
            if (links.length < 1) {
                continue;
            }
            for (var j = i; j < links.length; j++) {
                html += '<li class="list-group-item link-option" type=' + type + ' link="' + links[j].url + '">' + links[j].name + '</li>';
            }
        }
        $('.links-zone').html(html);
    }

    function renderLinkOptionBySelect() {
        var keyword = $('#link-name').val();
        var type = $('#link-type-zone').val();
        //深拷贝
        var renderData = JSON.parse(JSON.stringify(linksData));
        if (renderData.length > 0) {
            renderData = renderData.filter(function (item) {
                return item.type == type;
            });
            if (!!keyword) {
                if (renderData[0].list.length > 0) {
                    renderData[0].list = renderData[0].list.filter(function (item) {
                        return item.name.indexOf(keyword) > -1
                    });
                }
            }
            renderLinkOption(renderData);
        }
    }

    $(function () {
        var slide = $("#slide");
        var entrance = $("#entrance");
        var model1 = $("#model1");
        var model2 = $("#model2");
        //渲染数据
        render('slide');
        render('entrance');
        render('model1');
        renderModel2();
        renderGoods();
        renderLinks();
        //添加轮播图
        slide.find("span[name=add]").on('click', function () {
            var n = $("#slide tbody tr").length;
            addTr(n, 'slide', $("#slide tbody"), 'slide');
            // if (switchAddBtn($("#slide"), 4, n)) {
            //     addTr(n, 'slide', $("#slide tbody"), 'slide');
            //     var n = $("#slide tbody tr").length;
            //     switchAddBtn($("#slide"), 4, n);
            // }
        });
        //添加快捷入口
        entrance.find("span[name=add]").on('click', function () {
            var n = $("#entrance tbody tr").length;
            addTr(n, 'entrance', $("#entrance tbody"), 'entrance');
            // if (switchAddBtn($("#entrance"), 4, n)) {
            //     addTr(n, 'entrance', $("#entrance tbody"), 'entrance');
            //     var n = $("#entrance tbody tr").length;
            //     switchAddBtn($("#entrance"), 4, n);
            // }
        });
        model1.find("span[name=add]").on('click', function () {
            var n = $("#model1 tbody tr").length;
            addModel(n, 'model1', $("#model1 tbody"), 'model1');
            // if (switchAddBtn($("#model1"), 4, n)) {
            //     addModel(n, 'model1', $("#model1 tbody"), 'model1');
            //     var n = $("#model1 tbody tr").length;
            //     switchAddBtn($("#model1"), 4, n);
            // }
        });
        model2.find("span[name=add]").on('click', function () {
            $("#goods .chkbox").show();
            $("#goods span[name=sel]").show();
            $("#goods th.op span").show();
            var dom = $(this);
            renderGoods({}, function () {
                renderGoodsChk(dom);
            });
            $("#goods span[name=all-sel]").attr('model', 'model2');
            $.spa($("#goods"));
        });
        //保存
        slide.find("span[name=save]").on('click', function () {
            save(url.save, 1, 'slide');
        });
        //保存
        entrance.find("span[name=save]").on('click', function () {
            save(url.save, 2, 'entrance');
        });
        model1.find("span[name=save]").on('click', function () {
            save(url.save, 3, 'model1');
        });
        model2.find("span[name=save]").on('click', function () {
            saveTags();
        });
        $.spa($("body div:first"), function () {
            $(this).addClass('spa-page-wrap');
            $(this).find('').html();
        });
        $("span[name=search]").on('click', function () {
            var gname = $('#query_btn').prev('input').val().trim();
            var data = {
                gname: gname,
            };
            renderGoods(data, function () {
                var model = $("#goods span[name=all-sel]").attr('model');
                if (model) {
                    var trs = $("#" + model + " tbody tr");
                    var gids = [];
                    for (var i = 0; i < trs.length; i++) {
                        gids.push($(trs[i]).attr('gid'));
                    }
                }
                clearGoodsChk(gids);
            });
        });
    });

    function switchAddBtn(dom, num, n) {
        if (n >= num) {
            dom.find('th[name=op] span[name=add]').hide();
            return false;
        } else {
            dom.find('th[name=op] span[name=add]').show();
            return true;
        }
    }

    function renderGoodsChk(dom) {
        var trs = dom.closest('table').find('tbody tr');
        var gids = [];
        for (var i = 0; i < trs.length; i++) {
            var o = $(trs[i]);
            gids.push(o.attr('gid'));
        }
        if (gids.length > 0) {
            clearGoodsChk(gids);
        }
    }

    function clearGoodsChk(gids = []) {
        var chk = $("#goods input[type=checkbox]");
        for (var i = 0; i < chk.length; i++) {
            var dom = $(chk[i]);
            var gid = dom.closest('tr').attr('_gid');
            if ($.inArray(gid, gids) != -1) {
                dom.prop('checked', true);
            } else {
                dom.prop('checked', false);
            }
        }
    }

    function render(name = 'slide') {
        var list = data[name];
        var html = "";
        for (var i = 0; i < list.length; i++) {
            html += '<tr _no="' + list[i].id + '">';
            html += '<td><input type="hidden" name="photo" value="">';
            html += '<sapn id="' + list[i].id + '" class="browse_button btn btn-default btn-sm">选择</sapn></td>';
            html += '<td><img class="img" src="' + list[i].href + '"/></td>';
            html += '<td><input type="text" name="title" class="form-control input-sm input-width" value="' + list[i].title + '"></td>';
            if (name == 'model1') {
                html += '<td><input type="text" name="sub_title" class="form-control input-sm input-width" value="' + list[i].sub_title + '"></td>';
            }
            html += '<td><form class="form-inline"><div class="form-group"><input type="text" name="url" class="form-control input-sm input-width" value="' + list[i].url + '"></div><div class="form-group">' + getSelect() + '</div></form></td>';
            html += '<td name="op">';
            html += '<span class="btn btn-default btn-sm" name="del">删除</span>';
            html += '</td>';
            html += '</tr>'
        }
        $("#" + name + " tbody").append(html);
        for (var j = 0; j < list.length; j++) {
            var uuid = name + Math.random().toString(36).substr(2);
            $("#" + list[j].id).attr("id", uuid);
            bindUploadEvent($("#" + uuid), name);
        }
        if (name == 'model1') {
            renderTitle();
        }
        // switchAddBtn($("#" + name), 4, list.length);
    }

    function renderModel2() {
        var list = data.model2;
        var html = "";
        for (var i = 0; i < list.length; i++) {
            html += '<tr gid="' + list[i].id + '">';
            html += '<td><input type="text" name="gname" class="form-control input-sm input-width" value="' + list[i].gname + '"></td>';
            html += '<td><input type="text" name="tags" class="form-control input-sm input-width" value="' + list[i].tags + '"></td>';
            html += '<td name="op">';
            html += '<span class="btn btn-default btn-sm" name="del">删除</span>';
            html += '</td>';
            html += '</tr>';
            renderSelGood(list[i].id);
        }
        $("#model2 tbody").append(html);
        renderTitle();
    }

    function renderSelGood(gid) {
        $("#goods tbody tr[_gid=" + gid + "] td input[name=goods]").prop('checked', true);
    }

    function getSelect() {
        var html = '<div name="url" class="select-zone-container">';
        html += '<span  class="btn btn-default btn-sm" name="select-links">选择链接</span>';
        html += '<span class="btn btn-default btn-sm" name="goods">商品链接</span>';
        html += '</div>';
        return html;
    }

    function renderTitle() {
        $("#model1data input[name=config_title]").val(data.model_title[2]);
        $("#model2data input[name=config_title]").val(data.model_title[3]);
    }

    function bindUploadEvent(dom, type) {
        var self = dom;
        var browse_button_id = self.attr('id'),
            img = self.closest('tr').find('img:first'),
            input = self.prev('input:first');
        console.log(img)
        //构建uploader实例
        var uploader = new Qiniu.UploaderBuilder()
            .button(browse_button_id)
            .debug(true) //开启debug，默认false
            .domain({
                http: "http://upload.qiniup.com",
                https: "https://upload.qiniup.com"
            })
            .retry(0) //设置重传次数，默认0，不重传
            //.compress (0.5)//默认为1,范围0-1
            //.scale ([200, 0])//第一个参数是宽度，第二个是高度,[200,0],限定高度，宽度等比缩放.[0,100]限定宽度,高度等比缩放.[200,100]固定长宽
            .size(1024 * 1024) //分片大小，最多为4MB,单位为字节,默认1MB
            .chunk(true) //是否分块上传，默认true，当chunk=true并且文件大于4MB才会进行分块上传
            .auto(true) //选中文件后立即上传，默认true
            .multiple(true) //是否支持多文件选中，默认true
            .accept(['image/*']) //过滤文件，默认无，详细配置见http://www.w3schools.com/tags/att_input_accept.asp
            // 在一次上传队列中，是否分享token，如果为false每上传一个文件都需要请求一次token，默认true。
            // 如果saveKey中有需要在客户端解析的变量，则忽略该值。
            .tokenShare(true)
            // 设置token获取URL：客户端向该地址发送HTTP GET请求, 若成功，服务器端返回{"uptoken": 'i-am-token'}。
            .tokenUrl('/Admin/Public/ptoken')
            //任务拦截器
            .interceptor({
                //拦截任务,返回true，任务将会从任务队列中剔除，不会被上传
                onIntercept: function (task) {
                    return task.file.size > 1024 * 1024;
                },
                //中断任务，返回true，任务队列将会在这里中断，不会执行上传操作。
                onInterrupt: function (task) {
                    if (this.onIntercept(task)) {
                        $.alert("请上传小于1m的文件");
                        return true;
                    } else {
                        return false;
                    }
                }
            })
            .listener({
                onReady: function (tasks) {
                    //该回调函数在图片处理前执行,也就是说task.file中的图片都是没有处理过的
                    //选择上传文件确定后,该生命周期函数会被回调。
                },
                onStart: function (tasks) {
                    //所有内部图片任务处理后执行
                    //开始上传
                },
                onTaskGetKey: function (task) {
                    //return 'doc/img/111';
                    //为每一个上传的文件指定key,如果不指定则由七牛服务器自行处理
                },
                onTaskProgress: function (task) {
                    //每一个任务的上传进度,通过`task.progress`获取
                },
                onTaskSuccess: function (task) {
                    //一个任务上传成功后回调
                    var url = 'http://img.unionglasses.com/' + task.result._d.fkey
                    input.val(url);
                    img.attr('src', url);
                    console.log(img)
                    console.log(img.attr('src'))
                },
                onTaskFail: function (task) {
                    //一个任务在经历重传后依然失败后回调此函数
                },
                onTaskRetry: function (task) {
                    //开始重传
                },
                onFinish: function (tasks) {
                    //所有任务结束后回调，注意，结束不等于都成功，该函数会在所有HTTP上传请求响应后回调(包括重传请求)。
                }
            }).build();
    }

    function saveTags() {
        var config_title = $("#model2data input[name=config_title]").val() || "";
        if (config_title.length < 1) {
            $.alert('模块名称必填');
            return;
        }
        if (config_title && config_title.trim().length > 6) {
            $.alert('标题字数大于6个');
            return;
        }
        var trs = $("#model2data tbody tr");
        var data = [];
        for (var i = 0; i < trs.length; i++) {
            var tr = $(trs[i]);
            var gid = tr.attr('gid');
            if (!gid) {
                continue;
            }
            var o = {
                gid: gid,
                tags: tr.find('input[name=tags]').val().trim(),
            };
            data.push(o);
        }
        var pdata = {
            data: data,
            config_title: config_title,
        };
        var turl = url.savetags;
        $.ajaxExt(turl, pdata, function (jo) {
            if (jo.isOk()) {
                renderGoods({}, function () {
                    var trs = $("#model2data tbody tr");
                    for (var i = 0; i < trs.length; i++) {
                        var gid = $(trs[i]).attr('gid');
                        if (gid) {
                            renderSelGood(gid);
                        }
                    }
                });
                $.alert(jo.getMessage());
            } else {
                $.alert(jo.getMessage());
            }
        });
    }

    //保存
    function save(url, type, name) {
        var param = assembleData(name);
        if (param.length >= 1) {
            for (var i = 0; i < param.length; i++) {
                // if (param[i].title.length < 1) {
                //     $.alert('标题必填');
                //     return;
                // }
                if (param[i].title.length > 8) {
                    $.alert('标题字数大于8个');
                    return;
                }
                if (param[i].sub_title && param[i].sub_title.length > 8) {
                    $.alert('副标题字数大于8个');
                    return;
                }
            }
        }

        var config_title = $("#" + name + "data input[name=config_title]").val() || "";
        if (config_title && config_title.trim().length > 6) {
            $.alert('标题字数大于6个');
            return;
        }
        var pdata = {
            data: param,
            type: type,
            config_title: config_title,
        };
        var turl = url;
        $.ajaxExt(turl, pdata, function (jo) {
            if (jo.isOk()) {
                $.alert(jo.getMessage());
            } else {
                $.alert(jo.getMessage());
            }
        });
    }

    //组装e数据
    function assembleData(target) {
        var ret = [];
        var trs = $("#" + target + " tbody tr");
        for (var i = 0; i < trs.length; i++) {
            var o = $(trs[i]);
            var sub_title = o.find('input[name=sub_title]').val();
            var data = {
                id: o.attr('_no').trim(),
                href: o.find('img:first').attr('src').trim(),
                title: o.find('input[name=title]').val().trim(),
                sub_title: sub_title !== undefined ? sub_title.trim() : o.find('input[name=title]').val().trim(),
                url: o.find('input[name=url]').val().trim(),
            };
            ret.push(data);
        }
        return ret;
    }

    //添加
    function addTr(n, name = 'photo', dom, type) {
        var id = name + Math.random().toString(36).substr(2);
        var html = slideHtml.replace(/###/g, id);
        dom.append(html);
        bindUploadEvent($("#" + id), type);
    }

    function addModel(n, name = 'photo', dom, type) {
        var id = name + Math.random().toString(36).substr(2);
        var html = modelHtml.replace(/###/g, id);
        dom.append(html);
        bindUploadEvent($("#" + id), type);
    }

    function renderGoods(data = {}, callback = null) {
        var pdata = data;
        var turl = url.sellgoods;
        $.ajaxExt(turl, pdata, function (jo) {
            if (jo.isOk()) {
                var data = jo.data.list;
                tot = jo.data.tot;
                if (data.length < 1) {
                    return;
                }
                var html = '';
                for (var i = 0; i < data.length; i++) {
                    var photo = data[i].photo ? data[i].photo.split('\n')[0] : '';
                    var tags = encodeURI(data[i].tags) || '';
                    html += '<tr _gid = "' + data[i].id + '" _tags="' + tags + '">';
                    html += '<td class="chkbox"><input type="checkbox" name="goods"></td>';
                    html += '<td>' + data[i].gname + '</td>';
                    html += '<td><image class="img" src="' + photo + '"/></td>';
                    html += '<td class="op"><span class="btn btn-default btn-sm" name="link">复制链接</span>';
                    html += '<span class="btn btn-default btn-sm single-sel" name="sel">选择</span></td>';
                    html += '</tr>';
                }
                $("#goods tbody").html(html);
                if (callback) {
                    callback();
                }
            }
        });
    }

    function goodsLink(id, obj) {
        if (!id) {
            $.alert('无法复制');
            return;
        }
        var dataUrl = url.goodslink + '?id=' + id;
        var copy = new clipBoard($(obj), {
            beforeCopy: function () {
            },
            copy: function () {
                return dataUrl;
            },
            afterCopy: function () {
                $.alert('已复制');
                //$ (".spa-util-btn-back").trigger ('click');
            }
        });
    }

    //删除
    $(document).on('click', 'span[name=del]', function () {
        var dom = $(this);
        var tr = dom.closest('tr');
        dom.closest('table').find('th[name=op] span[name=add]').show();
        tr.remove();
    });

    //选择商品链接
    $(document).on('change', 'select[name=url]', function () {
        var dom = $(this);
        console.log(dom.val())
        var input = dom.closest('tr').find('input[name=url]');
        input.val(dom.val());
    });

    //商品链接
    $(document).on('click', 'span[name=goods]', function () {
        $("#goods span[name=all-sel]").attr('model', '');
        //renderGoods ({}, clearGoodsChk);
        renderGoods({}, renderView);
        $.spa($("#goods"));
    });


    var targetInput = null;

    //全站链接
    $(document).on('click', '.link-option', function () {
        var dom = $(this);
        var link = dom.attr('link');
        targetInput.val(link);
        targetInput.text(link);
        $('#selectLinkModal').modal('hide');
    });

    //选择链接
    $(document).on('click', 'span[name=select-links]', function () {
        var dom = $(this);
        targetInput = dom.closest('form').find('input[name=url]');
        $('#link-name').val('');
        $('#link-type-zone').val(0);
        renderLinkOptionBySelect();
        $('#selectLinkModal').modal('show');
    });

    function renderOptions() {
        var select = data.select;
        html += '<div class="select-option-zone">';
        for (var i = 0; i < select.length; i++) {
            html += '<div class="select-option" value = "' + select[i].url + '">';
            html += select[i].name;
            html += '</div>';
        }
        html += '</div>';
        return html;
    }

    function renderView() {
        $("#goods .chkbox").hide();
        $("#goods span[name=sel]").hide();
        $("#goods th.op span").hide();
    }

    $(document).on('click', 'span[name=link]', function () {
        var dom = $(this).closest('tr');
        var id = dom.attr('_gid');
        goodsLink(id, dom);
    });

    $(function () {
        $("#goods a[name=prev]").on('click', function () {
            if (page <= 1) {
                return;
            }
            page = page - 1;
            var data = {
                gname: $("#goods input[name=gname]").val().trim(),
                page: page,
            };
            renderGoods(data);
        });
        $("#goods a[name=next]").on('click', function () {
            if (page >= tot) {
                return;
            }
            page = page + 1;
            var data = {
                gname: $("#goods input[name=gname]").val().trim(),
                page: page,
            };
            renderGoods(data);
        });
    })

    $(document).on('click', '#goods span[name=all-sel]', function () {
        var model = $(this).attr('model');
        if (!model) {
            return;
        }
        var inputs = $("#goods table tbody input:checked");
        var html = '';
        var len = inputs.length;
        if (len > 8) {
            $.alert('只能选择8个商品');
            return false;
        }
        for (var i = 0; i < len; i++) {
            var o = $(inputs[i]).closest('tr');
            var gid = o.attr('_gid');
            var gname = o.find('td:first').next().text();
            var tag = decodeURI(o.attr('_tags') || '');
            html += '<tr gid="' + gid + '">';
            html += '<td>' + gname + '</td>';
            html += '<td><input type="text" name="tags" class="form-control input-sm input-width" value="' + tag + '"></td>';
            html += '<td name="op">';
            html += '<span class="btn btn-default btn-sm" name="del">删除</span>';
            html += '</td></tr>';
        }
        $("#model2data tbody").html(html);
        $(".spa-util-btn-back").trigger('click');
    });

    $(document).on('click', '.single-sel', function () {
        var dom = $(this).closest('tr').find('input:first');
        var model = $(this).closest('table').find('span[name=all-sel]').attr('model');
        if (!model) {
            return;
        }
        var limit = 0;
        if (model == 'model2') {
            limit = 8;
        }
        var chk = dom.is(":checked") ? 1 : 0;
        if (chk) {
            dom.prop('checked', false);
        } else {
            var n = $("#goods tbody input:checked").length;
            if (n >= limit) {
                return;
            }
            dom.prop('checked', true);
        }
    });

    $(document).on('click', '#goods input[name=goods]', function () {
        var dom = $(this);
        var chk = dom.prop('checked');
        if (chk) {
            var tr = dom.closest('tr');
            var gid = tr.attr('_gid');
            var gname = tr.find('td:first').next().text();
            var tag = decodeURI(tr.attr('_tags') || '');
            var html = '';
            html += '<tr gid="' + gid + '">';
            html += '<td><input type="text" name="gname" class="form-control input-sm input-width" value="' + gname + '"></td>';
            html += '<td><input type="text" name="tags" class="form-control input-sm input-width" value="' + tag + '"></td>';
            html += '<td name="op">';
            html += '<span class="btn btn-default btn-sm" name="del">删除</span>';
            html += '</td></tr>';
            var seltrs = $("#goods input[type='checkbox']:checked");
            var len = seltrs.length;
            for (var i = 0; i < len; i++) {
                var seltr = $(seltrs[i]).closest('tr');
                if (seltr.attr('_gid') == gid) {
                    if ((i - 1) < 0) {
                        $("#model2 table tbody").append(html);
                        break;
                    }
                    var ptr = $(seltrs[i - 1]).closest('tr');
                    var pgid = ptr.attr('_gid');
                    $("#model2 table tbody").find('tr[gid=' + pgid + ']').after(html);
                    break;
                }
            }
        } else {
            var tr = dom.closest('tr');
            var gid = tr.attr('_gid');
            $("#model2 table tbody").find('tr[gid=' + gid + ']').remove();
        }
    });

    $(document).on('keyup', '#link-name', function () {
        renderLinkOptionBySelect();
    });

    $(document).on('click', '#link-query', function () {
        renderLinkOptionBySelect();
    });

    $(document).on('change', '#link-type-zone', function () {
        renderLinkOptionBySelect();
    })
</script>
<div id="goods" class="container-fluid spa-page-wrap page" style="padding:10px;">
    <div class="form-inline query-box">
        <div class="form-group">
            <!--<label>商品名称 :</label>-->
            <input type="text" name="gname" class="form-control input-sm"/>
            <button id="query_btn" type="button" class="btn btn-sm admin-btn-default ml10">
                <span name="search"><i class="glyphicon glyphicon-search"></i>&nbsp;搜索</span>
            </button>
            <!--<span class="btn btn-default btn-sm" name="search">搜索</span>-->
        </div>
    </div>
    <table class="table table-bordered table-hover">
        <thead>
        <tr>
            <th class="chkbox">序号</th>
            <th>商品名称</th>
            <th>商品图片</th>
            <th class="op"><span class="btn btn-default btn-sm" name="all-sel" model="">确定</span></th>
        </tr>
        </thead>
        <tbody class="goods">
        </tbody>
    </table>
<!--    <div class="form-inline query-box" style="float:right">-->
<!--        <div class="form-group">-->
<!--            <a class="btn btn-sm btn-default" name="prev">上一页</a>-->
<!--            <a class="btn btn-sm btn-default" name="next">下一页</a>-->
<!--        </div>-->
<!--    </div>-->
</div>

</html>
