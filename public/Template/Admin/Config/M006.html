<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"/>
    <block name="title">
        <title>汉高微信后台管理</title>
    </block>
    <link rel="stylesheet" href="/ug/admin/css/bootstrap.min.css">
    <link rel="stylesheet" href="/ug/admin/css/tabpage.css">
    <link rel="stylesheet" href="/ug/admin/css/dialog.css">
    <link rel="stylesheet" href="/ug/b/css/add-coupons.css">
    <style type="text/css">
        #store-sel ul {
            list-style: none;
        }

        .img-view img {
            width: 200px;
            height: auto;
        }

        #coupon-form input {
            border-radius: 4px;
            padding: 6px 12px;
            border: 1px solid #dcdfe6;
        }

        .div-box-item .form-control {
            width: 70%;
        }
    </style>
</head>
<body>
<div class="content-add-body">
    <form class="form-horizontal" id="coupon-form">
        <div class="form-group">
            <label class="col-sm-2 control-label">会员权益：</label>
            <div class="col-sm-6">
                <textarea id="editor_wrap" name="detail" placeholder="会员权益"></textarea>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label"></label>
            <div class="col-sm-3">
                <a href="javascript:void(0);" class="btn btn-default-bg save">保存</a>
            </div>
        </div>
    </form>
</div>
</body>
<script type="text/javascript" src="/ug/js/jquery.min.js"></script>
<script type="text/javascript" src="/ug/js/plugincore.js"></script>
<script type="text/javascript" src="/ug/admin/js/dialog.js"></script>
<script type="text/javascript" src="/ug/admin/js/formvals.js"></script>
<script type="text/javascript" src="/manager/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="/ug/admin/ueditor/ueditor.all.min.js"></script>

<block name="js"></block>
<script type="text/javascript">
    var ue;
    var url = "#{$url}#";
    var data = "#{$data}#";

    $ (function () {
        loadUE ();
        renderData ();
    })

    function renderData () {
        ue.ready (function () {
            ue.setContent (data.des);
        });
    }

    $ ('.save').click (function () {
        if (!ue.getContent ()) {
            $.alert ('请填写会员权益');
            return;
        }
        var pdata = {
            mid: data.mid,
            des: ue.getContent (),
        };
        $.ajaxExt (url.do, pdata, function (jo) {
            if (jo.isOk ()) {
                $.alert ('保存成功', function () {
                    window.location.reload ();
                });
            } else {
                $.alert (jo.getMessage ());
            }
        });
    });

    function loadUE () {
        ue = UE.getEditor ('editor_wrap', {
            initialFrameHeight: 400,
            initialFrameWidth: 'auto',
            toolbars: [
                [
                    'undo', //撤销
                    'redo', //重做
                    'bold', //加粗
                    'indent', //首行缩进
                    'italic', //斜体
                    'underline', //下划线
                    'strikethrough', //删除线
                    'subscript', //下标
                    'fontborder', //字符边框
                    'superscript', //上标
                    'formatmatch', //格式刷
                    'source', //源代码
                    'blockquote', //引用
                    'pasteplain', //纯文本粘贴模式
                    'selectall', //全选
                    'horizontal', //分隔线
                    'removeformat', //清除格式
                    'time', //时间
                    'date', //日期
                    'link', //超链接
                    'unlink', //取消链接
                    '|',
                    'inserttable', //插入表格
                    'mergeright', //右合并单元格
                    'mergedown', //下合并单元格
                    'deleterow', //删除行
                    'deletecol', //删除列
                    'splittorows', //拆分成行
                    'splittocols', //拆分成列
                    'splittocells', //完全拆分单元格
                    'mergecells', //合并多个单元格
                    'deletetable', //删除表格
                ],
                [
                    'fontfamily', //字体
                    'fontsize', //字号
                    'paragraph', //段落格式
                    'justifyleft', //居左对齐
                    'justifyright', //居右对齐
                    'justifycenter', //居中对齐
                    'justifyjustify', //两端对齐
                    'forecolor', //字体颜色
                    'insertorderedlist', //有序列表
                    'insertunorderedlist', //无序列表
                    'fullscreen', //全屏
                    'rowspacingtop', //段前距
                    'rowspacingbottom', //段后距
                    'pagebreak', //分页
                    'lineheight', //行间距
                    'edittip ', //编辑提示
                    'autotypeset', //自动排版
                    'touppercase', //字母大写
                    'tolowercase', //字母小写
                    'map', //Baidu地图
                    'cleardoc', //清空文档
                    'simpleupload',
                    'insertimage'
                ]
            ]
        });
    }
</script>
</html>