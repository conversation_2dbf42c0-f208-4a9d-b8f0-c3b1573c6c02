<extend name="Base@Common/listtable" />
<block name="title">
	<title>活动列表</title>
</block>
<block name="css">
	<style type="text/css">
		.query-box>.form-group.query-group{
			margin-bottom: 0;
		}
	</style>
</block>
<block name="js">
	<script type="text/javascript" src="/manager/js/clipBoard.min.js"></script>
	<script src="/clientClub/js/qrcode3.js"></script>
	<script type="text/javascript">

		inf_dtbl.cols = [{
			col : 'c0',
			tit : '标题',
			rep : function(d) {
				return d.c0;
			}
		},{
			col : 'c1',
			tit : '活动时效',
			rep : function(d) {
				var str = '';
				if (d.c1 == 0) {
					str = '永久有效';
				} else if (d.c1 == 1) {
					str = d.c2 + '至' + d.c3;
					// str = d.c2.date('yyyy-MM-dd') + '至' + d.c3.date('yyyy-MM-dd');
				}
				return str;
			}
		},{
			col : 'c2',
			tit : '总点击量	',
			rep : function(d) {
				return d.c4;
			}
		},{
			col : 'c3',
			tit : '状态	',
			rep : function(d) {
				if (d.c5 == 0) {
					str = '已下架';
				} else if (d.c5 == 1) {
					str = '已上架';
				}
				return str
			}
		}];


		inf_dtbl.menus.dynamic = function(d) {
			var menus = [ ];
			menus.push({
				tag : 'link',
				title : '复制链接',
				icon : 'glyphicon glyphicon-link'
			});
			menus.push({
				tag : 'qrcode',
				title : '二维码',
				icon : 'glyphicon glyphicon-qrcode'
			});
			menus.push({
				tag : 'edit',
				title : '编辑',
				icon : 'glyphicon glyphicon-paperclip'
			});
			menus.push({
				tag : 'del',
				title : '删除',
				icon : 'glyphicon glyphicon-trash'
			});
			return menus;
		};

		inf_ext.menus.callback = function(tag, d){
			switch(tag){
				case 'qrcode':
					qrCode(d, $(this));
					break;
				case 'edit':
					edit(d, $(this));
					break;
				case 'link':
					copyUrl(d, $(this));
					break;
				default :
					break;
			}
		};
		function edit(data, obj){
			$.openTab('{$url.edit}'+'?id='+data.id, '修改活动');
		}
		function qrcode(data, obj)
		{
			var card_id = data.c2;
			var turl = '{$url.qrcode}';
			$.ajaxExt(turl, {card_id: card_id}, function(jo) {
				if (jo.isOk()) {
					$(".qrcode-img").attr('src',jo.url)
					$('#qrcode').dialog('扫码领取会员卡', function() {
					})
				} else {
					$.alert(jo.getMessage());
				}
			});

		}
		$(function(){
			$("#btn_add").on('click', function(){
				$.openTab('{$url.add}', '新增活动');
			})
		})
		function getCurrentUrl() {

			var url;

			url = window.location.protocol + '//' + window.location.host;

			if (window.location.port !== '') {
				url += ':' + window.location.port;
			}

			return url;
		}

		// 复制链接
		function copyUrl(data, obj) {

			var serviceUrl = getCurrentUrl();

			var dataUrl = serviceUrl + '{$url.link}' +'?id=' + data.c6;

			var copy = new clipBoard($(obj), {
				beforeCopy: function() {

				},
				copy: function() {
					return dataUrl;
				},
				afterCopy: function() {
					$.alert('已复制');
				}
			});
		}
		// 二维码
		function qrCode(data, obj) {
			$("#showQrcode").html('');
			var DOM = $("#showQrcode")[0];
			var qrcode = new QRCode(DOM, {
				width: 200,
				height: 200,
				colorDark: '#000000',
				colorLight: '#ffffff',
				correctLevel: QRCode.CorrectLevel.H
			});

			var serviceUrl = getCurrentUrl();

			qrcode.makeCode(serviceUrl + '{$url.link}' +'?id=' + data.c6);

			$('#mydialog').dialog('活动二维码').buttons();
		}


	</script>
</block>
<block name="query_ui">
	<div class="form-group query-group">
		<!--<label>活动名称</label> --><input type="text" class="form-control input-sm" cid="q{$k++}" placeholder="搜活动名称">
	</div>
</block>
<block name="tool_box">
	<button id="btn_add" type="button" class="btn btn-sm btn-default admin-btn-tab">
		<span class="glyphicon glyphicon-plus"></span>&nbsp;新增
	</button>
</block>

<block name="free_area">
	<div id="replydiv" style="display: none;">
		<div>
			<div>
				<form class="form-horizontal">
					<div class="form-group">
						<label class="col-sm-4 control-label">回复内容</label>
						<div class="col-sm-8">
							<textarea class="form-control anchor_c0" cid="c0" rows="3"></textarea>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
	<div id="qrcode" style="display: none;" class="col-sm-3">
		<div>
			<div>
				<div>
					<div>
						<img src="" class="qrcode-img" style="height: 250px;">
					</div>
				</div>
			</div>
		</div>
	</div>
	<div id="mydialog" style="display: none;">
		<div>
			<div>
				<div>
					<div id="showQrcode" style="width: 200px;margin: 0 auto;"></div>
				</div>
			</div>
		</div>
	</div>
</block>