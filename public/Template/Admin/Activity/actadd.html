<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no" />
    <block name="title">
        <title>汉高微信后台管理</title>
    </block>
    <link rel="stylesheet" href="/ug/admin/css/bootstrap.min.css">
    <link rel="stylesheet" href="/ug/admin/css/tabpage.css">
    <link rel="stylesheet" href="/ug/admin/css/dialog.css">
    <link rel="stylesheet" href="/ug/b/css/common.css">
    <link rel="stylesheet" href="/manager/assets/css/vip.css">
    <link rel="stylesheet" href="/ug/b/css/index.css">
    <link rel="stylesheet" href="/ug/b/css/add-list.css">

    <style type="text/css">
        #sel_img { width: 700px; }
        #sel_img .img-content li { border: 1px solid rgb(201,201,201);margin-right: 6px; margin-bottom: 6px; width: 120px; height: 120px; float:left; cursor: pointer; position: relative;overflow: hidden;}
        #sel_img .active .img_selected { display: inline-block; background: url('/manager/assets/img/icon_card_selected.png') no-repeat center center #000; position: absolute; left: 0; top: 0; width: 120px; height: 120px; background-color: rgba(0,0,0,0.7); filter:Alpha(opacity=70); }
        #filePicker .webuploader-pick {
            position: relative;
            top:8px;
            height: 26px;
            line-height: 26px;
            padding: 0 12px;
            background-color: rgb(3, 121, 214);
            font-size: 13px;
        }
        .radio-input:checked + .radioInput:after {
            background-color: rgb(3, 121, 214);
            border-radius: 100%;
            content: "";
            display: inline-block;
            height: 6px;
            width: 6px;
            position: absolute;
            top: 1px;
            right: 1px;
        }
        .required::before{
            content:"* ";
            color: red;
        }
        .sel_coupon input[type=checkbox] {
            vertical-align: top;
        }
        .form-ul .radio-inline{
            text-align: left;
        }
        .form-ul li > label {
            min-width: 100px;
        }
    </style>
</head>
<body>
<div class="container-fluid tabbody">
    <div class="main">
        <form class="form-horizontal" style="" id="vip-form">
            <div class="form-cont clear" style="">
                <ul class="form-ul" style="">

                    <li>
                        <label class="required">活动标题：</label>
                        <div class="input-group">
                            <input class="input-title" type="text" name="title" placeholder="请输入标题">
                        </div>
                    </li>
                    <li>
                        <label class="required">活动时效性：</label>
                        <div class="input-group">
                            <div class="div-box">
                                <!--<label class="radio-label space-txt">
                                    <input class="radio-input" type="radio" name="type" value="1" checked="checked">
                                    <span class="radioInput"></span>固定日期
                                </label>-->
                                <label class="radio-inline">
                                    <input type="radio" name="type" value="1" checked="checked">固定日期
                                </label>&nbsp;
                                <input type="text" style="width: 100px"  name="sdate" id="sdate"> <input type="text" style="width: 100px" id="edate" name="edate">
                            </div>
                            <div class="div-box">
                                <!--<label class="radio-label space-txt">
                                    <input class="radio-input" type="radio" name="type" value="0">
                                    <span class="radioInput"></span>永久有效
                                </label>-->
                                <label class="radio-inline">
                                    <input type="radio" name="type" value="0">永久有效
                                </label>
                            </div>
                        </div>
                    </li>
                    <li>
                        <label class="required">券可领取时间：</label>
                        <div class="input-group">
                            <div class="div-box">
                                <input type="text" style="width: 200px"  name="c_s_date" id="c_s_date" placeholder=" 可领取开始时间,为空不限制">
                                至
                                <input type="text" style="width: 200px"  name="c_e_date" id="c_e_date" placeholder=" 可领取结束时间,为空不限制">
                            </div>
                        </div>
                    </li>
                    <li>
                        <label class="required">编辑活动：</label>
                        <div class="input-group">
                            <script id="editor_wrap" name="content" type="text/plain">这里写你的初始化内容</script>
                        </div>
                    </li>
                    <li class="sel_coupon" style="">
                        <label>优惠券：</label>
                        <div class="input-group" style="margin-top: 4px">
                            <if condition="$coupons">
                                <foreach name="coupons" item="vo">
                                    <!--<label class="radio-label checkbox-label">
                                        <input class="radio-input" type="checkbox" name="coupon_id" value="{$vo.id}" <?php if(in_array($vo['id'], $selcoupons)) echo 'checked';?> >
                                        <span class="radio-checkbox radioInput"></span>{$vo.brandname}
                                    </label>-->
                                    <input class="checkbox-item" type="checkbox" name="coupon_id" value="{$vo.id}" <?php if(in_array($vo['id'], $selcoupons)) echo 'checked';?> > {$vo.title} &nbsp;
                                </foreach>
                                <else />
                                暂无优惠券
                            </if>
                        </div>
                    </li>
                    <li>
                        <label>状态：</label>
                        <if condition="$activity['status'] eq 1 ">
                            <div class="input-group">
                                <!--<label class="radio-label">
                                    <input class="radio-input" type="radio" name="status" value="1" checked="checked">
                                    <span class="radioInput"></span>已上架
                                </label>
                                <label class="radio-label last">
                                    <input class="radio-input" type="radio" name="status"  value="0">
                                    <span class="radioInput"></span>已下架
                                </label>-->
                                <label class="radio-inline">
                                    <input type="radio" name="status" value="1" checked="checked">已上架
                                </label>&nbsp;
                                <label class="radio-inline last">
                                    <input type="radio" name="status"  value="0">已下架
                                </label>
                            </div>
                            <else />
                            <div class="input-group">
                                <!--<label class="radio-label">
                                    <input class="radio-input" type="radio" name="status" value="1">
                                    <span class="radioInput"></span>已上架
                                </label>
                                <label class="radio-label last">
                                    <input class="radio-input" type="radio" name="status" checked="checked" value="0">
                                    <span class="radioInput"></span>已下架
                                </label>-->
                                <label class="radio-inline">
                                    <input type="radio" name="status" value="1">&nbsp;已上架
                                </label>
                                <label class="radio-inline last">
                                    <input type="radio" name="status" checked="checked" value="0">&nbsp;已下架
                                </label>
                            </div>
                        </if>
                    </li>
                    <li class="sel_coupon" style="display: none;">

                    </li>
                    <li>
                        <label style="visibility: hidden;">优惠券：</label>
                        <button id="btn_save" type="button" class="btn-default-size btn-default-bg save">保存</button>
                    </li>
                </ul>
            </div>
            <!--<div class="btn-box line">
                <a href="javascript:void(0);" class="btn-default-size btn-default-bg save">保存</a>
            </div>-->
        </form>
    </div>
</div>
</body>
<script type="text/javascript" src="/ug/js/jquery.min.js"></script>
<script type="text/javascript" src="/ug/js/plugincore.js"></script>
<script type="text/javascript" src="/ug/admin/js/dialog.js"></script>
<script type="text/javascript" src="/ug/admin/js/formvals.js"></script>
<script type="text/javascript" src="/manager/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="/manager/ueditor/ueditor.all.js"></script>
<script type="text/javascript" src="/ug/js/bootstrap.min.js"></script>
<script src="/manager/laydate/laydate.js"></script>
<script src="/manager/js/activity.js"></script>
<script>
    var ue;

    $(function() {
        laydate.render({
            elem: '[name=c_s_date]',
            type:'datetime'
        });
        laydate.render({
            elem: '[name=c_e_date]',
            type:'datetime'
        });
    });

    $('#vip-form .save').click(function() {

        var parm = $("#vip-form").serializeArray()
        var flag = true
        var activity_type = 0;
        var data = {}
        $.each(parm, function(index, item){
            data[item.name] = item.value
            if(item.name =='title' && item.value == ''){
                $.alert('标题不为空')
                flag = false
                return false
            }
            if(item.name =='type' ){
                console.log(item.value)
                activity_type = item.value
            }
            if(item.name =='sdate' && item.value == 0 && activity_type == 1){
                $.alert('开始时间不为空')
                flag = false
                return false
            }
            if(item.name =='edate' && item.value == 0 && activity_type == 1){
                $.alert('结束时间不为空')
                return false
            }
            if(item.name =='content' && item.value == '<p>这里写你的初始化内容</p>'){
                $.alert('请填写内容')
                flag = false
                return false
            }
        })
        console.log(data);
        if(flag){
            $.ajaxExt('{$url.addsave}', data, function(jo){
                if (jo.isOk()) {
                    $.alert('添加成功，请到列表页查看', function(){
                        window.location.reload();
                    })
                }else{
                    $.alert(jo.getMessage());
                }
            });
        }
    });
</script>
</html>