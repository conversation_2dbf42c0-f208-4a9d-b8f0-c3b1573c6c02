<extend name="Base@Admin/listtable" />
<block name="title">
<title>评价列表</title>
</block>
<block name="js"> <script type="text/javascript">

	inf_dtbl.cols = [{
		col : 'c0',
		tit : '标题',
		rep : function(d) {
			return d.c0;
		}
	},{
		col : 'c1',
		tit : '状态',
		rep : function(d) {
			var str = '';
			if (d.c1 == 0) {
				str = '待审核';
			} else if (d.c1 == 1) {
				str = '审核通过';
			} else if (d.c1 == 2) {
				str = '审核失败';
			} else {
				str = '删除';
			}
			return str;
		}
	}];
	

	inf_dtbl.menus.dynamic = function(d) {
		var menus = [ ];

			menus.push({
				tag : 'qrcode',
				title : '二维码',
				icon : 'glyphicon glyphicon-qrcode'
			});
//			menus.push({
//				tag : 'edit',
//				title : '编辑',
//				icon : 'glyphicon glyphicon-paperclip'
//			});
			menus.push({
				tag : 'del',
				title : '删除',
				icon : 'glyphicon glyphicon-trash'
			});
		return menus;
	};
	
	inf_ext.menus.callback = function(tag, d){
		switch(tag){
			case 'qrcode':
				qrcode(d, $(this));
				break;
			case 'edit':
				edit(d, $(this));
				break;
			default :
				break;
		}
	};
	function edit(data, obj){
		$.openTab('{$url.edit}'+'?card_id='+data.c2, '修改会员卡');
	}
	function qrcode(data, obj)
	{
		var card_id = data.c2;
		var turl = '{$url.qrcode}';
		$.ajaxExt(turl, {card_id: card_id}, function(jo) {
			if (jo.isOk()) {
				$(".qrcode-img").attr('src',jo.url)
				$('#qrcode').dialog('扫码领取会员卡', function() {
				}).buttons()
			} else {
				$.alert(jo.getMessage());
			}
		});

	}
	$(function(){
		$('#query_btn').hide()
		$("#btn_add").on('click', function(){
			$.openTab('{$url.add}', '新增会员卡');
		})
	})


</script> 
</block>

<block name="tool_box">
	<button id="btn_add" type="button" class="btn btn-sm btn-default admin-btn-tab">
		<span class="glyphicon glyphicon-plus"></span>&nbsp;新增
	</button>
</block>

<block name="free_area">
<div id="replydiv" style="display: none;">
	<div>
		<div>
			<form class="form-horizontal">
				<div class="form-group">
					<label class="col-sm-4 control-label">回复内容</label>
					<div class="col-sm-8">
						<textarea class="form-control anchor_c0" cid="c0" rows="3"></textarea>
					</div>
				</div>
			</form>
		</div>
	</div>
</div>
<div id="qrcode" style="display: none;">
	<div>
		<div>
			<div>
				<div style="width: 250px;margin: 0 auto;">
					<img src="" class="qrcode-img" style="height: 250px;">
				</div>
			</div>
		</div>
	</div>
</div>
</block>