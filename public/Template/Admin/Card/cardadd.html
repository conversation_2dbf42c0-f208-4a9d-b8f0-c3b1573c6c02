<!DOCTYPE html>
<html lang="zh-CN">
<head>
	<meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no" />
	<block name="title">
		<title>汉高微信后台管理</title>
	</block>
	<link rel="stylesheet" href="/manager/webuploader/webuploader.css">
	<link rel="stylesheet" href="/ug/admin/css/bootstrap.min.css">
	<link rel="stylesheet" href="/ug/admin/css/tabpage.css">
	<link rel="stylesheet" href="/ug/admin/css/dialog.css">
	<link rel="stylesheet" href="/ug/b/css/common.css">
	<link rel="stylesheet" href="/manager/assets/css/vip.css">
	<link rel="stylesheet" href="/ug/b/css/index.css">
	<link rel="stylesheet" href="/ug/b/css/add-list.css">

	<style type="text/css">
		#sel_img { width: 700px; }
		#sel_img .img-content li { border: 1px solid rgb(201,201,201);margin-right: 6px; margin-bottom: 6px; width: 120px; height: 120px; float:left; cursor: pointer; position: relative;overflow: hidden;}
		#sel_img .active .img_selected { display: inline-block; background: url('/manager/assets/img/icon_card_selected.png') no-repeat center center #000; position: absolute; left: 0; top: 0; width: 120px; height: 120px; background-color: rgba(0,0,0,0.7); filter:Alpha(opacity=70); }
		#filePicker .webuploader-pick {
			position: relative;
			top:8px;
			height: 26px;
			line-height: 26px;
			padding: 0 12px;
			background-color: rgb(3, 121, 214);
			font-size: 13px;
		}
		.radio-input:checked + .radioInput:after {
			background-color: rgb(3, 121, 214);
			border-radius: 100%;
			content: "";
			display: inline-block;
			height: 6px;
			width: 6px;
			position: absolute;
			top: 1px;
			right: 1px;
		}
		.required::before{
			content:"* ";
			color: red;
		}
	</style>
</head>
<body>
<div class="container-fluid tabbody">
	<div class="main">
		<form class="form-horizontal" style="" id="vip-form">
			<div class="form-cont clear" style="">
				<ul class="form-ul" style="">
					<li>
						<label class="required">卡券LOGO：</label>
						<div class="input-group">
							<input type="hidden" name="background_pic_url" value="http://mmbiz.qpic.cn/mmbiz_jpg/PaMib4c0ovVIAC1qqicibiciaFIOPTOArWMTwuvlOLL7icUspvUiaVfyYGeHib98qr77tpOvsCXeg1TnWDIFmYQurHjSLQ/0?wx_fmt=jpeg">
							<input type="hidden" name="background_local_url" value="http://mmbiz.qpic.cn/mmbiz_jpg/PaMib4c0ovVIAC1qqicibiciaFIOPTOArWMTwuvlOLL7icUspvUiaVfyYGeHib98qr77tpOvsCXeg1TnWDIFmYQurHjSLQ/0?wx_fmt=jpeg">
							<input type="hidden" name="logo_url" value="http://mmbiz.qpic.cn/mmbiz_jpg/PaMib4c0ovVIAC1qqicibiciaFIOPTOArWMTwuvlOLL7icUspvUiaVfyYGeHib98qr77tpOvsCXeg1TnWDIFmYQurHjSLQ/0?wx_fmt=jpeg">
							<input type="hidden" name="logo_local_url" value="http://mmbiz.qpic.cn/mmbiz_jpg/PaMib4c0ovVIAC1qqicibiciaFIOPTOArWMTwuvlOLL7icUspvUiaVfyYGeHib98qr77tpOvsCXeg1TnWDIFmYQurHjSLQ/0?wx_fmt=jpeg">
							<div class="clear img-wrap">
								<div class="up-img-wrap">
									<div class="img" id="up_logo">选择图片</div>
									<div class="tips">建议像素为300*300</div>
								</div>
								<div class="card-view-wrap">
									<div class="logo-view">
										<img src="" alt="" class="logo-img" style="border-radius: 20px;width: 50px;height: 50px;z-index: 100;top: 88px;position: absolute; left: 20px;display: inline-block;">
									</div>
								</div>
							</div>
						</div>
					</li>
					<li>
						<label class="required">卡券背景：</label>
						<div class="input-group">
							<input type="hidden" name="background_pic_url">
							<input type="hidden" name="local_url">
							<div class="clear img-wrap">
								<div class="up-img-wrap">
									<div class="img" id="up_img">选择图片</div>
									<div class="tips">像素大小控制在 1000像素*600像素以下</div>
								</div>
								<div class="card-view-wrap">
									<div class="card-view"></div>
									<div class="txt-center mt-8">会员卡预览</div>
								</div>
							</div>
						</div>
					</li>
					<li>
						<label class="required">会员卡标题：</label>
						<div class="input-group">
							<input class="input-title" type="text" name="title" placeholder="0 - 9个字">
						</div>
					</li>
					<li>
						<label class="required">有效期：</label>
						<div class="input-group">
							<div class="div-box">
								<label class="radio-inline">
		                            <input type="radio" name="type" value="1" checked="checked">固定日期
		                        </label>&nbsp;
		                        <input type="text" style="width: 100px"  name="sdate" id="sdate">--<input type="text" style="width: 100px" id="edate" name="edate">
								<!--<label class="radio-label space-txt">
									<input class="radio-input" type="radio" name="type" value="1" checked="checked">
									<span class="radioInput"></span>固定日期
								</label>
								<input type="text" style="width: 100px"  name="sdate" id="sdate">--<input type="text" style="width: 100px" id="edate" name="edate">-->
							</div>
							<div class="div-box">
								<label class="radio-inline">
		                            <input type="radio" name="type" value="0">永久有效
		                        </label>
								<!--<label class="radio-label space-txt">
									<input class="radio-input" type="radio" name="type" value="0">
									<span class="radioInput"></span>永久有效
								</label>-->
							</div>
						</div>
					</li>
					<li>
						<label class="required">时间段：</label>
						<div class="input-group">
							<label class="radio clear">
		                        <input type="radio" name="period_type" value="0" checked="checked">全部时段
		                    </label>
		                    <label class="radio clear">
		                        <input type="radio" name="period_type" value="1">部分时段
		                    </label>
							<!--<label class="radio-label clear">
								<input class="radio-input" type="radio" name="period_type" value="0" checked="checked">
								<span class="radioInput"></span>全部时段
							</label>
							<label class="radio-label clear">
								<input class="radio-input" type="radio" name="period_type" value="1">
								<span class="radioInput"></span>部分时段
							</label>-->
							<div class="times-tr" style="display:none;">
								<div class="div-box clear">
									<label class="wauto">日期：</label>
									<label class="radio-label checkbox-label">
										<input class="radio-input" type="checkbox" name="weekdays[]" value="1">
										<span class="radio-checkbox radioInput"></span>周一
									</label>
									<label class="radio-label checkbox-label">
										<input class="radio-input" type="checkbox" name="weekdays[]" value="2">
										<span class="radio-checkbox radioInput"></span>周二
									</label>
									<label class="radio-label checkbox-label">
										<input class="radio-input" type="checkbox" name="weekdays[]" value="3">
										<span class="radio-checkbox radioInput"></span>周三
									</label>
									<label class="radio-label checkbox-label">
										<input class="radio-input" type="checkbox" name="weekdays[]" value="4">
										<span class="radio-checkbox radioInput"></span>周四
									</label>
									<label class="radio-label checkbox-label">
										<input class="radio-input" type="checkbox" name="weekdays[]" value="5">
										<span class="radio-checkbox radioInput"></span>周五
									</label>
									<label class="radio-label checkbox-label">
										<input class="radio-input" type="checkbox" name="weekdays[]" value="6">
										<span class="radio-checkbox radioInput"></span>周六
									</label>
									<label class="radio-label checkbox-label">
										<input class="radio-input" type="checkbox" name="weekdays[]" value="7">
										<span class="radio-checkbox radioInput"></span>周日
									</label>
								</div>
								<div class="div-box">
									<label class="fleft wauto">时间：</label>
									<div class="div-box fleft add-timer-wrap">
										<input type="text" name="period_times_start[]">
										至
										<input type="text" name="period_times_end[]">
										<a href="javascript:void(0);" onclick="add_times(this)">添加时间段</a>
										<div class="tips">请使用24小时制输入时间，格式如 10:00 至 16:00 </div>
									</div>
								</div>
							</div>
						</div>
					</li>
					<li>
						<label class="required">会员卡优惠：</label>
						<div class="input-group col-md-2">
							 &nbsp;<input type="text" name="discount"> &nbsp;折(该会员卡享受的折扣优惠,填10就是九折)
						</div>
					</li>
					<li>
						<label class="required">特权说明：</label>
						<div class="input-group">
							<textarea class="textarea-default" name="privileged_description" placeholder="会员卡详情"></textarea>
						</div>
					</li>
					<li>
						<label class="required">使用须知：</label>
						<div class="input-group">
							<textarea class="textarea-default" name="remark" placeholder="使用须知"></textarea>
						</div>
					</li>
					<li>
						<label class="required">操作提示：</label>
						<div class="input-group">
							<input type="text" name="instruction" placeholder="字数请限制在16字以内">
						</div>
					</li>
					<li>
						<label></label>
						<a href="javascript:void(0);" class="btn btn-default-bg btn-default-size save">保存</a>
					</li>
				</ul>
			</div>
			<!--<div class="btn-box line">
				<a href="javascript:void(0);" class="btn btn-success save">保存</a>
			</div>-->
		</form>
	</div>
</div>
</body>
<script type="text/javascript" src="/ug/js/jquery.min.js"></script>
<script type="text/javascript" src="/ug/js/plugincore.js"></script>
<script type="text/javascript" src="/ug/admin/js/dialog.js"></script>
<script type="text/javascript" src="/ug/admin/js/formvals.js"></script>
<script type="text/javascript" src="/manager/webuploader/webuploader.min.js"></script>
<script type="text/javascript" src="/manager/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="/manager/ueditor/ueditor.all.js"></script>
<script type="text/javascript" src="/ug/js/bootstrap.min.js"></script>
<script src="/manager/laydate/laydate.js"></script>
<script type="text/javascript" src="/manager/js/card.js"></script>
<script>
	$('#vip-form .save').click(function() {

		var form = get_form_serialize_array('vip-form');
		var parm = format_data(form)
		if(parm !== false){
			$.ajaxExt('{$url.addsave}', parm, function(jo){
				if (jo.isOk()) {
					$.alert('添加成功!', function(){
						window.location.reload();
					})

				}
			});
		}
	});
</script>
</html>