<!DOCTYPE html>
<html lang="zh-CN">
<head>
	<meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no" />
	<block name="title">
		<title>汉高微信后台管理</title>
	</block>
	<link rel="stylesheet" href="/manager/webuploader/webuploader.css">
	<link rel="stylesheet" href="/ug/admin/css/bootstrap.min.css">
	<link rel="stylesheet" href="/ug/admin/css/tabpage.css">
	<link rel="stylesheet" href="/ug/admin/css/dialog.css">
	<link rel="stylesheet" href="/ug/b/css/common.css">
	<link rel="stylesheet" href="/manager/assets/css/vip.css">
	<link rel="stylesheet" href="/ug/b/css/index.css">
	<link rel="stylesheet" href="/ug/b/css/add-list.css">

	<style type="text/css">
		#sel_img { width: 700px; }
		#sel_img .img-content li { border: 1px solid rgb(201,201,201);margin-right: 6px; margin-bottom: 6px; width: 120px; height: 120px; float:left; cursor: pointer; position: relative;overflow: hidden;}
		#sel_img .active .img_selected { display: inline-block; background: url('/manager/assets/img/icon_card_selected.png') no-repeat center center #000; position: absolute; left: 0; top: 0; width: 120px; height: 120px; background-color: rgba(0,0,0,0.7); filter:Alpha(opacity=70); }
		#filePicker .webuploader-pick {
			position: relative;
			top:8px;
			height: 26px;
			line-height: 26px;
			padding: 0 12px;
			background-color: rgb(3, 121, 214);
			font-size: 13px;
		}
		.radio-input:checked + .radioInput:after {
			background-color: rgb(3, 121, 214);
			border-radius: 100%;
			content: "";
			display: inline-block;
			height: 6px;
			width: 6px;
			position: absolute;
			top: 1px;
			right: 1px;
		}
		.required::before{
			content:"* ";
			color: red;
		}
	</style>
</head>
<body>
<div class="container-fluid tabbody">
	<div class="main">
		<form class="form-horizontal" style="" id="vip-form">
			<div class="form-cont clear" style="">
				<ul class="form-ul" style="">
					<li>
						<label class="required">卡券背景：</label>
						<div class="input-group">
							<input type="hidden" name="card_id" value="{$card.card_id}" >
							<input type="hidden" name="logo_url" value="{$card.logo_url}" >
							<input type="hidden" name="local_url" value="{$card.local_url}">
							<div class="clear img-wrap">
								<div class="up-img-wrap">
									<div class="img" id="up_img">选择图片</div>
									<div class="tips">建议您上传145*70的图稿大小，格式为png jpeg</div>
								</div>
								<div class="card-view-wrap">
									<div class="card-view">
										<img src="{$card.local_url}" style="width: auto; height: 142px;">
									</div>
									<div class="txt-center mt-8">会员卡预览</div>
								</div>
							</div>
						</div>
					</li>
					<li>
						<label class="required">会员卡标题：</label>
						<div class="input-group">
							<input class="input-title" type="text" name="title" placeholder="0 - 9个字" value="{$card.title}">
						</div>
					</li>
					<li>
						<label class="required">有效期：</label>
						<div class="input-group">
							<div class="div-box">
								<label class="radio-label space-txt">
									<if condition="$card['type'] eq 1 ">
										<input class="radio-input" type="radio" name="type" value="1" checked="checked">
										<else />
										<input class="radio-input" type="radio" name="type" value="1" >
									</if>
									<span class="radioInput"></span>固定日期
								</label>
								<input type="text" style="width: 100px"  name="sdate" id="sdate" value="{$card.start_date}">--<input type="text" style="width: 100px" id="edate" name="edate" value="{$card.end_date}">
							</div>
							<div class="div-box">
								<label class="radio-label space-txt">
									<if condition="$card['type'] eq 1 ">
										<input class="radio-input" type="radio" name="type" value="0">
										<else />
										<input class="radio-input" type="radio" name="type" value="0" checked>
									</if>
									<span class="radioInput"></span>永久有效
								</label>
							</div>
						</div>
					</li>
					<li>
						<label class="required">时间段：</label>
						<div class="input-group">
							<label class="radio-label clear">
								<if condition="$card['type'] eq 0 ">
									<input class="radio-input" type="radio" name="period_type" value="0" checked="checked">
									<else />
									<input class="radio-input" type="radio" name="period_type" value="0" >
								</if>
								<span class="radioInput"></span>全部时段
							</label>
							<label class="radio-label clear">
								<if condition="$card['type'] eq 0 ">
									<input class="radio-input" type="radio" name="period_type" value="1">
									<else />
									<input class="radio-input" type="radio" name="period_type" value="1" checked="checked">
								</if>
								<span class="radioInput"></span>部分时段
							</label>
							<if condition="$card['type'] eq 0 ">
								<div class="times-tr" style="display:none;">
								<else />
									<div class="times-tr" style="">
							</if>
								<div class="div-box clear">
									<label class="wauto">日期：</label>
									<label class="radio-label checkbox-label">
										<?php
											if(strpos($card['weekdays'], '1') !== false){
										?>
										<input class="radio-input" type="checkbox" name="weekdays[]" value="1" checked="checked">
										<?php
											}else{
										?>
										<input class="radio-input" type="checkbox" name="weekdays[]" value="1" >
										<?php
											}
										?>
										<span class="radio-checkbox radioInput"></span>周一
									</label>
									<label class="radio-label checkbox-label">
										<?php
											if(strpos($card['weekdays'], '2') !== false){
										?>
										<input class="radio-input" type="checkbox" name="weekdays[]" value="2" checked="checked">
										<?php
											}else{
										?>
										<input class="radio-input" type="checkbox" name="weekdays[]" value="2" >
										<?php
											}
										?>
										<span class="radio-checkbox radioInput"></span>周二
									</label>
									<label class="radio-label checkbox-label">
										<?php
											if(strpos($card['weekdays'], '3') !== false){
										?>
										<input class="radio-input" type="checkbox" name="weekdays[]" value="3" checked="checked">
										<?php
											}else{
										?>
										<input class="radio-input" type="checkbox" name="weekdays[]" value="3" >
										<?php
											}
										?>
										<span class="radio-checkbox radioInput"></span>周三
									</label>
									<label class="radio-label checkbox-label">
										<?php
											if(strpos($card['weekdays'], '4') !== false){
										?>
										<input class="radio-input" type="checkbox" name="weekdays[]" value="4" checked="checked">
										<?php
											}else{
										?>
										<input class="radio-input" type="checkbox" name="weekdays[]" value="4" >
										<?php
											}
										?>
										<span class="radio-checkbox radioInput"></span>周四
									</label>
									<label class="radio-label checkbox-label">
										<?php
											if(strpos($card['weekdays'], '5') !== false){
										?>
										<input class="radio-input" type="checkbox" name="weekdays[]" value="5" checked="checked">
										<?php
											}else{
										?>
										<input class="radio-input" type="checkbox" name="weekdays[]" value="5" >
										<?php
											}
										?>
										<span class="radio-checkbox radioInput"></span>周五
									</label>
									<label class="radio-label checkbox-label">
										<?php
											if(strpos($card['weekdays'], '6') !== false){
										?>
										<input class="radio-input" type="checkbox" name="weekdays[]" value="6" checked="checked">
										<?php
											}else{
										?>
										<input class="radio-input" type="checkbox" name="weekdays[]" value="6" >
										<?php
											}
										?>
										<span class="radio-checkbox radioInput"></span>周六
									</label>
									<label class="radio-label checkbox-label">
										<?php
											if(strpos($card['weekdays'], '7') !== false){
										?>
										<input class="radio-input" type="checkbox" name="weekdays[]" value="7" checked="checked">
										<?php
											}else{
										?>
										<input class="radio-input" type="checkbox" name="weekdays[]" value="7" >
										<?php
											}
										?>
										<span class="radio-checkbox radioInput"></span>周日
									</label>
								</div>
								<div class="div-box">
									<label class="fleft wauto">时间：</label>
									<if condition="$card['period_type'] == 1">
										<div class="div-box fleft add-timer-wrap">
											<?php
												foreach($card['period_times'] as $key => $item){
												if($key == 0){
											?>
													<input type="text" name="period_times_start[]" value="<?php echo $item[0]; ?>">
													至
													<input type="text" name="period_times_end[]" value="<?php echo $item[1]; ?>">
													<a href="javascript:void(0);" onclick="add_times(this)">添加时间段</a>
													<div class="tips">请使用24小时制输入时间，格式如 10:00 至 16:00 </div>
											<?php
												}else{
											?>
												<div class="div-box fleft">
												<input type="text" name="period_times_start[]" value="<?php echo $item[0]; ?>">
												至
												<input type="text" name="period_times_end[]" value="<?php echo $item[1]; ?>">
												<a href="javascript:void(0);" onclick="add_times(this)">添加时间段</a>
												<a class="input-txt" href="javascript:void(0);" onclick="del_times(this)">删除时间段</a>
												</div>
											<?php
												}
												}
											?>
										<else />
										<div class="div-box fleft add-timer-wrap">
										<input type="text" name="period_times_start[]">
										至
										<input type="text" name="period_times_end[]">
										<a href="javascript:void(0);" onclick="add_times(this)">添加时间段</a>
										<div class="tips">请使用24小时制输入时间，格式如 10:00 至 16:00 </div>
									</div>
									</if>

								</div>
							</div>
						</div>
					</li>
					<li>
						<label class="required">会员卡优惠：</label>
						<div class="input-group">
							折扣优惠，用户享 &nbsp;<input type="text" name="discount" value="{$card.discount}"> &nbsp;折
						</div>
					</li>
					<li>
						<label class="required">特权说明：</label>
						<div class="input-group">
							<textarea class="textarea-default" name="privileged_description" placeholder="会员卡详情">{$card.privileged_description}</textarea>
						</div>
					</li>
					<li>
						<label class="required">使用须知：</label>
						<div class="input-group">
							<textarea class="textarea-default" name="remark" placeholder="使用须知">{$card.remark}</textarea>
						</div>
					</li>
					<li>
						<label class="required">操作提示：</label>
						<div class="input-group">
							<input type="text" name="instruction" placeholder="字数请限制在16字以内" value="{$card.instruction}">
						</div>
					</li>
				</ul>
			</div>
			<div class="btn-box line">
				<a href="javascript:void(0);" class="btn btn-success save">保存</a>
			</div>
		</form>
	</div>
</div>
</body>
<script type="text/javascript" src="/ug/js/jquery.min.js"></script>
<script type="text/javascript" src="/ug/js/plugincore.js"></script>
<script type="text/javascript" src="/ug/admin/js/dialog.js"></script>
<script type="text/javascript" src="/ug/admin/js/formvals.js"></script>
<script type="text/javascript" src="/manager/webuploader/webuploader.min.js"></script>
<script type="text/javascript" src="/manager/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="/manager/ueditor/ueditor.all.js"></script>
<script type="text/javascript" src="/ug/js/bootstrap.min.js"></script>
<script src="/manager/laydate/laydate.js"></script>
<script type="text/javascript" src="/manager/js/card.js"></script>
<script>
	$('#vip-form .save').click(function() {

		var form = get_form_serialize_array('vip-form');
		var parm = format_data(form)
		if(parm !== false){
			$.ajaxExt('{$url.editsave}', parm, function(data){
				return
				if (data.code == 0) {
					$.toast('操作成功，请到列表页更新查看',2000,1000,function(){
						window.location.reload();
						$.openInf("It's me").my("注意");
					});
				}
			});
		}
	});
</script>
</html>