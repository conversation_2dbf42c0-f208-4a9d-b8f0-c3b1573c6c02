<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no"/>
    <block name="title">
        <title>批量发券</title>
    </block>
    <link rel="stylesheet" href="/ug/admin/css/bootstrap.min.css">
    <link rel="stylesheet" href="/ug/admin/css/dialog.css">
    <style type="text/css">
        .tips {
            color: red;
        }
    </style>
</head>
<body>
<div class="content-add-body">
    <form class="form-horizontal">
        <div class="form-group">
            <label class="col-sm-2 control-label">导入用户：</label>
            <div class="col-sm-6">
                <input type="file" name="file" class="form-control">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label"></label>
            <div class="col-sm-6 tips">
                请先导入用户 <a href="https://newwx.unionglasses.com/uploads/material/2021-11-01/617f5e1bd8853.xlsx">导入模版</a>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">选择券：</label>
            <div class="col-sm-6">
                <select class="w-normal fleft mr-small form-control" name="coupon_id">
                    <notempty>
                        <foreach name="coupons" item="coupon">
                            <option value="{$coupon.id}">{$coupon.title}</option>
                        </foreach>
                    </notempty>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label"></label>
            <div class="col-sm-3">
                <a href="javascript:void(0);" class="btn btn-default-bg save">批量发券</a>
            </div>
        </div>
    </form>
</div>
</body>
<script type="text/javascript" src="/ug/js/jquery.min.js"></script>
<script type="text/javascript" src="/ug/js/plugincore.js"></script>
<script type="text/javascript" src="/ug/admin/js/dialog.js"></script>
<script type="text/javascript" src="/ug/admin/js/formvals.js"></script>
<block name="js">
    <script type="text/javascript">
        var users = [];

        $('.save').click(function () {
            var couponId = $('[name=coupon_id]').val();
            if (users.length == 0) {
                $.alert('请先导入用户');
                return false;
            }
            $.ajaxExt('{$url.save}', {users: users, coupon_id: couponId}, function (result) {
                if (result._c == 0) {
                    var data = result.data;
                    if (data.length > 0) {
                        var errphone = [];
                        for (var i = 0; i < data.length; i++) {
                            errphone.push(data[i].phone);
                        }
                        $.alert(errphone.join(',') + '发放失败，其余的成功');
                    } else {
                        $.alert('发放成功');
                    }
                } else {
                    $.alert('失败');
                }
            });
        });

        $('[name=file]').change(function() {
            var formData = new FormData();
            formData.append('file', $('[name=file]')[0].files[0]);
            $(this).val('');
            $.ajax({
                url: '{$url.upload}',
                method: 'POST',
                data: formData,
                dataType: 'json',
                contentType: false,
                processData: false,
                cache: false,
                success: function(r) {
                    $.ajaxExt('{$url.import}', {
                        filename: r.data
                    }, function(result) {
                        if (result._c == 0) {
                            users = result.data;
                            $('.tips').html('已成功导入' + users.length + '，失败' + result.unnum);
                        } else {
                            users = [];
                        }
                    });
                },
                error: function (jqXHR) {
                    console.log(jqXHR);
                }
            })
        })
    </script>
</block>
</html>