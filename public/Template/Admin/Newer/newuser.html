<!DOCTYPE html>
<html lang="zh-CN">

	<head>
		<meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
		<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no" />
		<block name="title">
			<title>汉高微信后台管理</title>
		</block>
		<link rel="stylesheet" href="/ug/admin/css/bootstrap.min.css">
		<link rel="stylesheet" href="/ug/admin/css/tabpage.css">
		<link rel="stylesheet" href="/ug/admin/css/dialog.css">
		<link rel="stylesheet" href="/ug/b/css/common.css">
		<link rel="stylesheet" href="/manager/assets/css/vip.css">
		<link rel="stylesheet" href="/ug/b/css/index.css">
		<link rel="stylesheet" href="/ug/b/css/add-list.css">
		<link rel="stylesheet" href="/manager/webuploader/webuploader.css">
		<!--<link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.6-rc.0/css/select2.min.css" rel="stylesheet"/>-->
		<link href="/res/ace/css/select2-4.0.6.min.css" rel="stylesheet"/>

		<style type="text/css">
			#sel_img {
				width: 700px;
			}
			
			#sel_img .img-content li {
				border: 1px solid rgb(201, 201, 201);
				margin-right: 6px;
				margin-bottom: 6px;
				width: 120px;
				height: 120px;
				float: left;
				cursor: pointer;
				position: relative;
				overflow: hidden;
			}
			
			#sel_img .active .img_selected {
				display: inline-block;
				background: url('/manager/assets/img/icon_card_selected.png') no-repeat center center #000;
				position: absolute;
				left: 0;
				top: 0;
				width: 120px;
				height: 120px;
				background-color: rgba(0, 0, 0, 0.7);
				filter: Alpha(opacity=70);
			}
			
			#filePicker .webuploader-pick {
				position: relative;
				top: 4px;
				height: 26px;
				line-height: 26px;
				padding: 0 12px;
				background-color: rgb(79, 136, 173);
				font-size: 13px;
			}
			
			.radio-input:checked+.radioInput:after {
				background-color: rgb(3, 121, 214);
				border-radius: 100%;
				content: "";
				display: inline-block;
				height: 6px;
				width: 6px;
				position: absolute;
				top: 1px;
				right: 1px;
			}
			
			.required::before {
				content: "* ";
				color: red;
			}
			
			.btn-primary {
				color: #333;
			}
			
			/*.btn-success {
				background-color: rgb(79, 136, 173);
			}*/
			
			.sel-coupon .btn {
				margin: 5px 5px 0 0;
			}
			.badge{
				padding: 2px;
				right: -4px;
			}
			.ml10 {
				margin-left: 11px;
			}
			
			.ml4 {
				margin-left: 4px;
			}
			
			.input-group {
				width: auto;
			}
			
			.table-box .title {
				text-align: right;
			}
			
			.table-box td {
				font-size: 14px;
				padding: 6px;
				vertical-align: top
			}
			
			.table-box td>span {
				width: 120px;
				text-align: left;
				float: left;
				/*padding: 4px 0;*/
			}
			
			.dropdown,
			.sel-coupon {
				float: left;
			}
			.checkbox-item{
				vertical-align: middle;
			}
			input.checkbox-item{
				margin: -1px 4px 0 0;
			}
			.badge{
				font-size: 14px;
				background-color: rgb(79, 136, 173);
			}
			/*.img{*/
				/*position: absolute;*/
				/*left: 300px;;*/
			/*}*/
			.select2-container--default .select2-selection--single {
				border: 1px solid #ccc !important;
				border-radius: 3px !important;
				height: 34px;
			}

			.select2-container--default .select2-selection--single .select2-selection__rendered {
				color: #555;
				font-size: 12px;
				line-height: 34px;
			}
		</style>
	</head>

	<body>
		<div class="container-fluid tabbody">
			<div class="main">
				<table class="table-box">
					<tbody>
						<tr>
							<td class="title">标题：</td>
							<td class="input-group ml4">
								<input rows="6" cols="28" name="title" value="<?php echo $new['title'];?>">
							</td>
						</tr>
						<tr>
							<td class="title" style="padding-top: 8px;">上传背景：</td>
							<td class="input-group ml4">
								<span id="filePicker">选择文件</span>
							</td>
						</tr>
						<tr>
							<td class="title" style="padding-top: 8px;">背景：</td>
							<td class="input-group ml4">
								<div class="img">
									<img src="<?php echo $new['img'];?>" alt="" width="80" height="80">
								</div>
							</td>
						</tr>
						<tr>
							<input type="hidden" name="id" value="<?php echo $new['id'];?>">
							<td class="title">活动时间：</td>
							<td class="input-group ml4">
								<if condition="$new['type'] eq 1 ">
									<div class="div-box">
										<label class="radio-inline">
				                            <input type="radio" name="type" value="1" checked="checked">固定日期
				                        </label>&nbsp;
										<input type="text" style="width: 100px"  name="sdate" id="sdate" value="{$new.sdate}">--<input type="text" style="width: 100px" id="edate" name="edate" value="{$new.edate}">
									</div>
									<div class="div-box">
										<label class="radio-inline">
				                            <input type="radio" name="type" value="0">永久有效
				                        </label>
									</div>
								<else />
									<div class="div-box">
										<label class="radio-inline">
				                            <input type="radio" name="type" value="1" checked="checked">固定日期
				                        </label>&nbsp;
										<input type="text" style="width: 100px"  name="sdate" id="sdate" value="">--<input type="text" style="width: 100px" id="edate" name="edate" value="">
									</div>
									<div class="div-box">
										<label class="radio-inline">
				                            <input type="radio" name="type" value="0" checked="checked"> 永久有效
				                        </label>
									</div>
								</if>
							</td>
						</tr>
						<tr>
							<td class="title">新人礼包说明：</td>
							<td class="input-group ml4">
								<textarea rows="6" cols="28" name="remark"><?php echo $new['remark'];?></textarea>
							</td>
						</tr>
						<tr>
							<td class="title">新人优惠券：</td>
							<td class="input-group ml4">
								<span><input class="checkbox-item" type="checkbox" name="item" data-item='score' <?php if($new[ 'score']) echo 'checked';?>> 赠送积分：</span>
								<input class="input-title" type="text" name="score" placeholder="请输入赠送积分值" value="<?php echo $new['score'] ? $new['score']:''; ?>">
							</td>
						</tr>
						<tr>
							<td></td>
							<td class="input-group ml4">
								<span>
									<input class="checkbox-item" type="checkbox" name="item" data-item='money' <?php if(intVal($new['money'])) echo 'checked';?>>送红包：
								</span>
								<input class="input-title" type="text" name="money" placeholder="请输入红包金额" value="<?php echo intVal($new['money']) ?$new['money']:''; ?>">
							</td>
						</tr>
						<tr>
							<td></td>
							<td class="input-group ml4">
								<span>
									<input class="checkbox-item" type="checkbox" name="item" data-item='cou' <?php if($new['cou_cids']) echo 'checked';?>>优惠券：
								</span>
								<div class="input-group form-inline ml10" style="display:inline-block;margin-top: 4px">
									<select class="form-control  list-query select-ext" cid="coupon" id="coupon_id" multiple="multiple" style="width: 450px">

									</select>
								</div>
							</td>
						</tr>

						<tr>
							<td class="title">推荐人礼包：</td>
							<td class="input-group ml4">
								<span class="">
									<input class="checkbox-item" type="checkbox" name="item" data-item='rec_score' <?php if($new['rec_score']) echo 'checked';?>>赠送积分：
								</span>
								<input class="input-title" type="text" name="rec_score" placeholder="请输入赠送积分值" value="<?php echo $new['rec_score'] ? $new['rec_score']:''; ?>">
							</td>
						</tr>
						<tr>
							<td></td>
							<td class="input-group ml4">
								<span class="">
									<input class="checkbox-item" type="checkbox" name="item" data-item='rec_money' <?php if(intVal($new['rec_money'])) echo 'checked';?>>送红包：
								</span>
								<input class="input-title" type="text" name="rec_money" placeholder="请输入红包金额" value="<?php echo intVal($new['rec_money']) ?$new['rec_money']:''; ?>">
							</td>
						</tr>
						<tr>
							<td></td>
							<td class="input-group ml4">
								<span class="">
									<input class="checkbox-item" type="checkbox" name="item" data-item='rec_cou' <?php if($new['rec_cou_cids']) echo 'checked';?>>优惠券：
								</span>
								<div class="input-group form-inline ml10" style="display:inline-block;margin-top: 4px">
									<select class="form-control  list-query select-ext" cid="rec_coupon" id="rec_coupon_id" multiple="multiple" style="width: 450px">

									</select>
								</div>
							</td>
						</tr>

						<tr>
							<td class="title">须知: </td>
							<td class="input-group ml4" style="color: red;">新人礼包发放是每位新用户领取，仅凭手机号发放</td>
						</tr>
						<tr>
							<td class="title" style="visibility: hidden;">须知: </td>
							<td class="input-group ml4"><button type="button" class="btn btn-default-size btn-default-bg save">保存</button></td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</body>
	<script type="text/javascript" src="/ug/js/jquery.min.js"></script>
	<script type="text/javascript" src="/ug/js/plugincore.js"></script>
	<script type="text/javascript" src="/ug/admin/js/dialog.js"></script>
	<script type="text/javascript" src="/ug/admin/js/formvals.js"></script>
	<script type="text/javascript" src="/ug/js/bootstrap.min.js"></script>
	<script type="text/javascript" src="/manager/webuploader/webuploader.min.js"></script>
	<script src="/manager/laydate/laydate.js"></script>
	<!--<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.6-rc.0/js/select2.min.js"></script>-->
	<script type="text/javascript" src="/res/ace/js/select2-4.0.6.min.js"></script>

	<script>
		uploader = WebUploader.create({
			auto: true,
			swf: '/manager/webuploader/Uploader.swf',
			server: '/Admin/newer/uploadimg',
			formData: {
				m_type: 'image'
			},
			pick: '#filePicker',
			duplicate: true,
			accept: {
				title: 'Images',
				extensions: 'jpg,jpeg,png',
				mimeTypes: 'image/jpg,image/jpeg,image/png'
			}
		});
		uploader.on( 'uploadSuccess', function(file, response) {
			if (response._c == 1) {
				$(".img img").attr('src',response.data)
			}else {
				alert(response._m);
			}
		});

		var coupon_list = "#{$coupon_list}#";
		var rec_coupon_list = "#{$rec_coupon_list}#";
		console.log(coupon_list)

		$(function () {
			initTimePicker();

			load_coupon();
			load_rec_coupon();
			$ ('.select-ext').select2 ({
				"language": {
					"noResults": function () {
						return "没有数据";
					}
				},
				escapeMarkup: function (markup) {
					return markup;
				}
			});
		})

		function initTimePicker(){
			var myDate = new Date();
			var year=myDate.getFullYear();
			var month=myDate.getMonth()+1;
			var date=myDate.getDate();
			var now = year + '-' +month + '-' + date;
			var sdate = {
				elem: '#sdate',
				type:'date',
				min: now,
				show: true,
				closeStop: '#sdate'

			};
			var edate = {
				elem: '#edate',
				type:'date',
				show: true,
				closeStop: '#edate'
			};
			lay('#sdate').on('click', function(e){
				if($('#sdate').val() != null && $('#sdate').val() != undefined && $('#sdate').val() != ''){
					sdate.max = $('#edate').val();
				}
				laydate.render(sdate);
			});
			lay('#edate').on('click', function(e){
				if($('#edate').val() != null && $('#edate').val() != undefined && $('#receive_stime').val() != ''){
					edate.min = $('#sdate').val();
				}
				laydate.render(edate);
			});


		}

		$(".save").on('click', function() {
			var checked = []
			$("input[type='checkbox']").each(function(index, ele) {
				if($(ele).is(':checked')) {
					checked.push($(ele).data('item'))
				}
			})
			var score = $("input[name='score']").val()
			var money = $("input[name='money']").val()
			var rec_score = $("input[name='rec_score']").val()
			var rec_money = $("input[name='rec_money']").val()
			var type = $("input[name='type']:checked").val()
			var sdate = $("input[name='sdate']").val()
			var edate = $("input[name='edate']").val()
			var remark = $("textarea[name='remark']").val()
			var title = $("input[name='title']").val()
			if(title =='') {
				$.alert('请输入标题')
				return
			}
			if(type == '1' && (sdate == '' || edate=='')) {
				$.alert('请选择有效时间')
				return
			}
			if(($.inArray('score', checked) != -1 && isNaN(score)) || ($.inArray('rec_score', checked) != -1 && isNaN(rec_score))) {
				$.alert('赠送积分必须是数字')
				return
			}
			if(($.inArray('money', checked) != -1 && isNaN(money)) || ($.inArray('rec_money', checked) != -1 && isNaN(rec_money))) {
				$.alert('红包金额必须是数字')
				return
			}
			var id = $("input[name='id']").val()
			var img = $(".img img").attr('src')
			var coupon_id = $("#coupon_id").val();
			var rec_coupon_id = $("#rec_coupon_id").val();

			var param = {
				id: id,
				title:title,
				type: type,
				img:img,
				sdate:sdate,
				edate:edate,
				remark: remark,
				cou: coupon_id,
				score: score,
				money: money,
				rec_cou: rec_coupon_id,
				rec_score: rec_score,
				rec_money: rec_money,
				checked: checked
			};

			$.ajaxExt('/admin/newer/newusersave', param, function(jo) {
				if(jo.isOk()) {
					$.alert('修改成功', function() {
						window.location.reload();
					})

				} else {
					$.alert(jo.getMessage());
				}
			});
		})

		function load_coupon() {
			$.post('{$url.couponList}', {type: ''}, function(res) {
				if (res._c == 0) {
					var option = '';
					var item = res.data, length = item.length;
					for (var i=0; i<length; i++) {
						var sel = '';
						if (coupon_list.includes(item[i].id))
						{
							sel = ' selected="selected"';
						}
						option += '<option value="' + item[i].id + '" ' + sel + '>' + item[i].title + '</option>';
					}
					$('[cid=coupon]').html(option);
				}
			}, 'json');
		}

		function load_rec_coupon () {
			$.post('{$url.couponList}', {type: ''}, function(res) {
				if (res._c == 0) {
					var option = '';
					var item = res.data, length = item.length;
					for (var i=0; i<length; i++) {
						var sel = '';
						if (rec_coupon_list.includes(item[i].id))
						{
							sel = ' selected="selected"';
						}
						option += '<option value="' + item[i].id + '" ' + sel + '>' + item[i].title + '</option>';
					}
					$('[cid=rec_coupon]').html(option);
				}
			}, 'json');
		}
	</script>

</html>