<extend name="Base@Common/listtable"/>
<block name="title">
    <title>开店设置</title>
</block>
<block name="js">
    <script type="text/javascript">

        inf_dtbl.cols = [{
            col: 'c0',
            tit: '设置',
            rep: function (d) {
                var text = '';
                switch (d.c0) {
                    case '1':
                        text = '开店条件';
                        break;
                        break;
                }
                return text;
            }
        }, {
            col: 'c1',
            tit: '条件明细',
            rep: function (d) {
                var html = '';
                var data = d.c1 ? JSON.parse (d.c1) : null;
                if (!data) {
                    return html;
                }
                html += '1. 线下订单:';
                html += '<br/>';
                html += '订单金额>=' + data.offline.ccmoney + '元 ';
                html += data.offline.logic == 'or' ? '或者 ' : '并且 ';
                html += '订单数量>=' + data.offline.ccnumber + '次 ';
                html += '<hr/>';
                html += '2. 线上订单:';
                html += '<br/>';
                html += '订单金额>=' + data.online.ccmoney + '元 ';
                html += data.online.logic == 'or' ? '或者 ' : '并且 ';
                html += '订单数量>=' + data.online.ccnumber + '次 ';
                html += '<hr/>';
                if (data.logic == 'or') {
                    html += '条件1和条件2满足其中一项既可开店'
                } else {
                    html += '条件1和条件2满足同时满足既可开店'
                }
                return html;
            }
        }];

        inf_dtbl.menus.dynamic = function (d) {
            var menus = [];
            menus.push ({
                tag: 'upShopCond',
                title: '设置',
                icon: 'glyphicon glyphicon-pencil'
            });
            return menus;
        };

        inf_ext.menus.callback = function (tag, d) {
            switch (tag) {
                case 'upShopCond':
                    upShopCond (d, $ (this));
                    break;
                default :
                    break;
            }
        };

        function render (data) {
            var renddata = data.c1 ? JSON.parse (data.c1) : '';
            console.log(renddata)
            if (renddata != '') {
                $ ("#openshopcond input[cid=c0]").val (renddata.offline.ccmoney);
                $ ("#openshopcond input[cid=c1]").val (renddata.offline.ccnumber);
                $ ("#openshopcond input[cid=c2]").val (renddata.online.ccmoney);
                $ ("#openshopcond input[cid=c3]").val (renddata.online.ccnumber);
                $ ("#openshopcond input[cid='c4'][value='"+ renddata.is_show +"']").attr("checked",true);
            }
        }

        function upShopCond (data, obj) {
            render (data);
            $ ('#openshopcond').dialog ('设置', function () {
                var pdata = {
                    id: data.id,
                };
                $.formvals ($ (this), 'cid', pdata);
                for (var k in pdata) {
                    var anchor = $ (this).find ('.anchor_' + k);
                    if (!check (pdata[k])) {
                        anchor.poptip ('请设置正确的数值');
                        anchor.trigger ('focus');
                        return false;
                    }
                }
                var turl = '{$url.openshopcond}';
                $.ajaxExt (turl, pdata, function (jo) {
                    if (jo.isOk ()) {
                        tpl_obj.tblctl.load (false);
                    } else {
                        $.alert (jo.getMessage ());
                    }
                });
            })
        }

        function check (v) {
            var re = /^\d+(?=\.{0,1}\d+$|$)/;
            var ret = true;
            if (v != "") {
                if (!re.test (v)) {
                    ret = false;
                }
            }
            return ret;
        }
    </script>
</block>

<block name="free_area">
    <div id="openshopcond" style="display: none;">
        <form class="form-horizontal">
            <div class="form-group">
                <label class="col-sm-5 control-label">线下订单金额:</label>
                <div class="col-sm-6">
                    <input class="form-control anchor_c0" cid="c0">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-5 control-label">线下订单次数:</label>
                <div class="col-sm-6">
                    <input class="form-control anchor_c1" cid="c1">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-5 control-label">线上订单金额:</label>
                <div class="col-sm-6">
                    <input class="form-control anchor_c2" cid="c2">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-5 control-label">线上订单次数:</label>
                <div class="col-sm-6">
                    <input class="form-control anchor_c3" cid="c3">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-5 control-label">是否允许开店:</label>
                <div class="col-sm-6">
                    <label class="radio-inline">
                        <input type="radio" class="anchor_c5" name="range" cid="c4" value="1" checked="">允许
                    </label>
                    <label class="radio-inline">
                        <input type="radio" class="anchor_c5" name="range" cid="c4" value="2">不允许
                    </label>
                </div>

            </div>
        </form>
    </div>
</block>