<extend name="Base@Common/listtable"/>
<block name="title">
    <title>门店列表</title>
</block>
<block name="css">
    <style>
        .table {
            width: 100%;
            font-size: 14px;
            background-color: #fff;
            /*text-align: center;*/
            border-top: 1px solid #e7e7eb;
            border-bottom: 1px solid #e7e7eb;
            border-spacing: 0;
        }

        .qrcode_size .table {
            border: 1px solid #e7e7eb;
            overflow: hidden;
            width: 100%;
            font-size: 14px;
            background-color: #fff;
            text-align: center;
            border-spacing: 0;
        }

        .thead {
            background-color: #f4f5f9;
        }

        .qrcode_size .table_cell:first-child {
            width: 200px;
        }

        .thead .table_cell:first-child {
            border-left-width: 0;
        }

        .qrcode_size .table_cell {
            color: #666;
            text-align: center;
        }

        .thead .table_cell {
            border-left-width: 0;
            line-height: 40px;
            vertical-align: middle;
        }

        .thead .table_cell {
            line-height: 32px;
            border-bottom: 1px solid #e7e7eb;
        }

        .table_cell {
            padding-left: 20px;
            padding-right: 20px;
        }

        .table_cell {
            padding: 0;
            font-weight: 400;
            font-style: normal;
        }

        .tbody tr:first-child .table_cell {
            border-top-width: 0;
        }

        .qrcode_size .table_cell:first-child {
            width: 200px;
            text-align: center;
        }

        .qrcode_size .table_cell {
            color: #666;
        }

        tbody .table_cell {
            padding-top: 9px;
            padding-bottom: 9px;
            border-top: 1px solid #e7e7eb;
        }

        .qrcode_size_tips {
            color: #8d8d8d;
            padding-top: .5em;
        }

        .icon {
            width: 18px;
            height: 18px;
            vertical-align: middle;
            display: inline-block;
            line-height: 100px;
            overflow: hidden;
        }

        .download_gray {
            background: url(/ug/admin/img/icon-download.png) 0 0 no-repeat;
        }

        .download_gray:hover {
            background: url(/ug/admin/img/icon-download.png) 0 -21px no-repeat;
        }

        #qrcode {
            display: none;
        }

        .dialog-box {
            width: 760px;
        }
    </style>
</block>
<block name="js">
    <script src="/ug/js/jquery.qrcode.min.js"></script>
    <script type="text/javascript" src="/manager/js/clipBoard.min.js"></script>
    <script type="text/javascript">
        inf_dtbl.cols = [{
            col: 'c0',
            tit: '公司名称'
        }, {
            col: 'c1',
            tit: '公司编号'
        }, {
            col: 'pname',
            tit: '上级公司',
            rep: function (d) {
            }
        }];

        inf_dtbl.menus.dynamic = function (d) {
            var menus = [];
            menus.push ({
                tag: 'qrcode',
                title: '二维码',
                icon: 'glyphicon glyphicon-qrcode'
            });
            menus.push ({
                tag: 'copyurl',
                title: '复制链接',
                icon: 'glyphicon glyphicon-link'
            });
            return menus;
        };

        inf_ext.menus.callback = function (tag, d) {
            switch (tag) {
                case 'qrcode':
                    qrcodeModal (d);
                    break;
                case 'copyurl':
                    copyurl (d);
                    break;
                default:
                    break;
            }
        };
        var resUrl = '';

        //因为下载按钮处于隐藏状态，对于隐藏元素的操作要用上面的方法比较保险
        function qrcodeModal (data) {
            $ ('#moreSize').dialog ('尺寸选择');
            resUrl = data.url;
            $ (document).on ('click', '.download_gray', function () {
                var _this = $ (this);
                var size = 224;
                switch (parseInt ($ (this).attr ('_val').trim ())) {
                    case 8:
                        setQrcode (_this, size);
                        break;
                    case 12:
                        size = 336;
                        setQrcode (_this, size);
                        break;
                    case 15:
                        size = 420;
                        setQrcode (_this, size);
                        break;
                    case 30:
                        size = 840;
                        setQrcode (_this, size);
                        break;
                    case 50:
                        size = 1400;
                        setQrcode (_this, size);
                        break;
                }
            });
        }

        function setQrcode (objs, size) {
            $ ('#qrcode').html ('');
            var code = $ ('#qrcode').qrcode ({
                render: 'canvas', //生成二维码的格式还有image、div
                text: resUrl,
                background: "#ffffff",
                fill: "#333333", //二维码纹路的颜色
                fontcolor: "#ff9818",
                width: size,
                height: size
            });
            var canvas = code.find ('canvas').get (0);
            url = canvas.toDataURL ('image/jpg');
            objs.attr ("href", url).attr ("download", "二维码.png");
            objs.find ('img').attr ("src", url);
        }

        function copyurl (data) {
            var dataUrl = data.url;
            var copy = new clipBoard ($ (this), {
                beforeCopy: function () {
                },
                copy: function () {
                    return dataUrl;
                },
                afterCopy: function () {
                    $.alert ('已复制');
                }
            });
        }

        $ (function () {
        })
    </script>
</block>

<block name="query_ui">
    <div class="form-group">
        <!--<label>门店名称</label> --><input type="text" class="form-control input-sm" cid="q{$k++}" placeholder="搜门店名称">
    </div>
</block>
<block name="tool_box">
</block>
<block name="free_area">
    <div class="table_wrp qrcode_size" id="moreSize" style="display: none;width: 56%;">
        <div>
            <div>
                <div id="qrcode">
                    <img/>
                </div>
                <table class="table" cellspacing="0">
                    <thead class="thead">
                    <tr>
                        <th class="table_cell">二维码边长(cm)</th>
                        <th class="table_cell">建议扫描距离(米)</th>
                        <th class="table_cell no_extra">下载链接</th>
                    </tr>
                    </thead>
                    <tbody class="tbody">
                    <tr>
                        <td class="table_cell"> 8cm</td>
                        <td class="table_cell"> 0.5m</td>
                        <td class="table_cell">
                            <a class="icon download_gray download-btn" _val="8">下载<img width="224" height="224"></a>
                        </td>
                    </tr>
                    <tr>
                        <td class="table_cell"> 12cm</td>
                        <td class="table_cell"> 0.8m</td>
                        <td class="table_cell">
                            <a class="icon download_gray download-btn" _val="12">下载</a>
                        </td>
                    </tr>
                    <tr>
                        <td class="table_cell"> 15cm</td>
                        <td class="table_cell"> 1m</td>
                        <td class="table_cell">
                            <a class="icon download_gray download-btn" _val="15">下载</a>
                        </td>
                    </tr>
                    <tr>
                        <td class="table_cell"> 30cm</td>
                        <td class="table_cell"> 1.5m</td>
                        <td class="table_cell">
                            <a class="icon download_gray download-btn" _val="30">下载</a>
                        </td>
                    </tr>
                    <tr>
                        <td class="table_cell"> 50cm</td>
                        <td class="table_cell"> 2.5m</td>
                        <td class="table_cell">
                            <a class="icon download_gray download-btn" _val="50">下载</a>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <p class="qrcode_size_tips">二维码尺寸请按照43像素的整数倍缩放，以保持最佳效果</p>
            </div>
        </div>
    </div>
</block>