<!DOCTYPE html>
<html lang="zh-CN">

	<head>
		<meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
		<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no" />
		<title>汉高微信后台管理</title>
		<link rel="stylesheet" href="/ug/admin/css/bootstrap.min.css">
		<link rel="stylesheet" href="/ug/admin/css/dialog.css">
		<style type="text/css">
			body {
				padding: 10px;
			}
			
			.table>tbody>tr>td,
			.table>tbody>tr>th,
			.table>tfoot>tr>td,
			.table>tfoot>tr>th,
			.table>thead>tr>td,
			.table>thead>tr>th {
				border-top: none;
			}
			
			.form-control {
				box-shadow: none;
				-webkit-box-shadow: none;
			}
			
			.form-inline>div.radio {
				margin-top: 6px;
			}
			
			.form-inline .form-group>input {
				margin-right: 4px;
				padding: 5px 8px;
			}
			
			.panel-default {
				width: 720px;
			}
			
			.panel .table {
				margin-bottom: 0px;
			}
			
			.panel .table th {
				width: 120px;
			}
			
			.panel .table tbody tr:first-child>* {
				border-top: 0px;
			}
			
			.panel .panel-body {
				padding: 5px 15px;
			}
			.btn{
				background: rgb(79, 136, 173);
				border-color: rgb(79, 136, 173);
			}
			.btn:hover{
				background: rgb(79, 136, 173);
				border-color: rgb(79, 136, 173);
			}
		</style>
	</head>

	<body>
		<div class="container-fluid tabbody">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h3 class="panel-title">分销佣金比例设置</h3>
				</div>
				<div class="panel-body">
					<table class="table table-hover">
						<tbody>
							<tr>
								<th>一级佣金比例</th>
								<td>
									<input type="text" class="form-control input-sm" name="level1" value="{$rate_info.level1}" style="width: 40%">
								</td>
							</tr>
							<tr>
								<th>二级佣金比例</th>
								<td>
									<input type="text" class="form-control input-sm" name="level2" value="{$rate_info.level2}" style="width: 40%">
								</td>
							</tr>
							<tr>
								<th>三级佣金比例</th>
								<td>
									<input type="text" class="form-control input-sm" name="level3" value="{$rate_info.level3}" style="width: 40%">
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
			<div>
				<button id="btn-ok" type="button" class="btn btn-primary admin-btn-submit">保存</button>
			</div>
		</div>

	</body>
	<script type="text/javascript" src="/ug/js/jquery.min.js"></script>
	<script type="text/javascript" src="/ug/js/plugincore.js"></script>
	<script type="text/javascript" src="/ug/admin/js/dialog.js"></script>
	<script type="text/javascript" src="/ug/admin/js/formvals.js"></script>
	<script src="/ug/js/bootstrap.min.js"></script>
	<script type="text/javascript">
		$(function() {
			$("#btn-ok").bind('click', function() {
				var _turl = '{$url.save}';

				var level1 = $("input[name=level1]").val();
				var level2 = $("input[name=level2]").val();
				var level3 = $("input[name=level3]").val();
				if (!checkRate(level1) || !checkRate(level2) || !checkRate(level3)){
					$.alert('请输入正确的佣金比例');
					return;
				}
				if (level1>1 || level2>1 || level3>1){
					$.alert('佣金比例不大于1');
					return;
				}

				var _pdata = {
					level1: level1,
					level2: level2,
					level3: level3
				};

				$.ajaxExt(_turl, _pdata, function(jo) {
					if(jo.isOk()) {
						$.alert('操作成功', function() {
							window.location.reload();
						});
					} else {
						$.alert(jo.getMessage());
					}
				});
			});

			function checkRate(level) {
				var reg = /^(0|[1-9]\d*)(\s|$|\.\d{1,2}\b)/;
				return reg.test(level);
			}
		})
	</script>

</html>