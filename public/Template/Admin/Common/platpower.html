<extend name="Base@Admin/listtable" />
<block name="title">
<title>平台员工</title>
</block>
<block name="js"> <script type="text/javascript">
	inf_dtbl.cols = [ {
		col : 'c0',
		sortable : true,
		tit : '帐号',
	}, {
		col : 'c1',
		tit : '创建时间',
		rep : function(d) {
			return d.c1.date('yyyy-MM-dd hh:mm:ss');
		}
	}, {
		col : 'c2',
		tit : '状态',
		rep : function(d) {
			if (d.c2 == '1') {
				return '<font style="color:green;">正常</font>';
			} else {
				return '<font style="color:red;">已禁用</font>';
			}
		}
	} ];

	inf_dtbl.menus.rows.push({
		tag : 'topower',
		title : '分配权限',
		icon : 'glyphicon glyphicon-floppy-disk'
	});

	inf_ext.menus.callback = function(tag, d) {
		switch (tag) {
		case 'topower':
			$.openTab('{$url.power}?id=' + d.id, d.c0 + ' 权限');
			break;
		default:
			break;
		}
	};
</script> </block>
<block name="query_ui">
<div class="form-group">
	<label>帐号</label> <input type="text" class="form-control" cid="q{$k++}" placeholder="搜索帐号">
</div>
</block>