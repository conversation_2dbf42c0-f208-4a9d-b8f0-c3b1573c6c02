<!DOCTYPE html>
<html lang="zh-CN">

	<head>
		<meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
		<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no" />
		<meta name="format-detection" content="telephone=no" />
		<block name="title">
			<title>后台管理系统</title>
		</block>
		<link rel="stylesheet" href="/ug/admin/css/bootstrap.min.css">
		<link rel="stylesheet" href="/ug/b/css/common.css">
		<link rel="stylesheet" href="/ug/b/css/default-skin.css">
		<link rel="stylesheet" href="/ug/b/css/modal-box.css">
		<link rel="stylesheet" href="/ug/b/css/left.css">
		<link rel="stylesheet" href="/ug/b/css/header.css">
		<style type="text/css">
			html,
			body {
				height: 100%;
				overflow: hidden;
				min-width: 700px;
			}
			
			::-webkit-scrollbar {
				/*隐藏滚轮*/
				display: none;
			}
			
			li.arrow-down>a>.arrow {
				width: 14px;
				background: url(/ug/b/img/icon-arrow-down.png) no-repeat center;
			}
			
			.icon-refresh {
				width: 20px;
				height: inherit;
				background: url(/ug/b/img/btn-refresh.png) no-repeat center center;
				float: left;
				padding-left: 10px;
				padding-right: 4px;
				cursor: pointer;
			}
			
			#tab-forward>.border-r {
				float: left !important;
			}
			
			#qrcodeHide {
				position: relative;
				margin-right: 14px;
			}
			
			#qrcodeHide:hover #qrcodeShow {
				display: block;
			}
			
			.qrcodeHide_box {
				display: none;
				width: 120px;
				height: 120px;
				position: absolute;
				background: #f8f8f8;
				padding: 10px;
				left: 0px;
				top: 87px;
				border: 1px solid #ddd;
				border-top: none;
				z-index: 1;
			}
			
			#qrcodeHide>.unbind-txt,
			#unbind-btn>.unbind-txt {
				color: #ffa83b;
			}
			
			.qrcode-box {
				width: 84px;
				height: 84px;
			}
			
			.none {
				display: none;
			}
			a.notice-box{
				position: relative;
				color: #fff;
			}
			.notice-box .glyphicon-bell{
				font-size: 16px;
			}
			.admin-badge{
				font-size: 12px;
			    line-height: 1.4;
			    position: absolute;
			    top: 20px;
			    left: 100%;
			    margin-left: -8px;
			    padding: 1px 5px;
			    color: #fff;
			    background: red;
			    border-radius: 50%;
			    display: none;
			}
			.noice-wrap{
				position: absolute;
				display: none;
				font-size: 14px;
				min-width: 200px;
				max-width: 300px;
				padding-bottom: 6px;
				background-color: #f3f3f3;
			}
			.notice-title{
				padding: 6px 12px;
				overflow: hidden;
				border-bottom: 1px solid #ccc;
			}
			.noice-list{
				margin: 0;
				padding: 0;
				list-style: none;
			}
			.noice-list>li{
				color: #333;
				padding: 4px 12px;
				white-space: nowrap;
				text-overflow: ellipsis;
				border-bottom: 1px solid #ccc;
			}
			.notice-info,.notice-time{
				font-size: 14px;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
			.notice-time{
				font-size: 12px;
			}
			.notice-view-more{
				display: block;
				font-size: 12px;
				padding-top: 6px;
				text-align: center;
			}
			.empty-info{
				padding-top: 6px;
				text-align: center;
			}
			/*左边菜单start*/
			.l-menu-aside {
				width: 72px;
				position: relative;
				height: 100%;
				overflow-x: hidden;
				overflow-y: auto;
				background: url(/ug/b/img/left-menu-samll.png) bottom center no-repeat #2285d9;
			}
			
			.l-menu-aside.show {
				background: url(/ug/b/img/left-menu-medium.png) bottom center no-repeat #2285d9;
			}
			
			.l-menu-aside.show .menu-list {
				display: block;
			}
			
			.l-menu-aside .menu-list {
				display: none;
			}
			
			.menu-list {
				opacity: 0;
			}
			
			.l-menu-aside.show .menu-list-1>li,
			.l-menu-aside.show .menu-icon-main {
				position: relative;
				background: url(/ug/b/img/l-m-b.png) no-repeat bottom left;
			}
			
			.l-menu-1 {
				position: relative;
				height: 56px;
			}
			
			.show .l-menu-1 {
				height: 45px;
			}
			
			.menu-1-icon {
				position: absolute;
				width: 42px;
				cursor: pointer;
				right: 15px;
				top: 15px;
				color: #94caec;
				-webkit-transition: -webkit-transform 0.6s ease;
				-moz-transition: -moz-transform 0.6s ease;
				-o-transition: -o-transform 0.6s ease;
				-ms-transition: -ms-transform 0.6s ease;
			}
			
			.menu-1-icon:hover {
				color: #fff;
			}
			
			.show .menu-1-icon {
				right: 30px;
				top: 8px;
			}
			
			.arrow-triangle {
				width: 0;
				height: 0;
				border-width: 5px 8px;
				border-style: solid;
				position: absolute;
				top: 8px;
				right: -9px;
			}
			
			.nav-glyph {
				position: absolute;
				top: 0;
				left: 0;
				background: transparent;
				border: none;
				-webkit-transition: all .5s ease;
				-moz-transition: all .5s ease;
				-ms-transition: all .5s ease;
				-o-transition: all .5s ease;
				transition: all .5s ease;
				cursor: pointer;
				z-index: 99999;
			}
			
			.icon-bar {
				display: block;
				margin: 2px 0 6px;
				width: 30px;
				height: 3px;
				background-color: #94caec;
				border-radius: 10px;
			}
			
			.menu-1-icon:hover .icon-bar {
				background-color: #fff;
			}
			
			.show .menu-1-icon .arrow-triangle {
				right: 2px;
				border-color: transparent #94caec transparent transparent;
			}
			
			.show .menu-1-icon:hover .arrow-triangle {
				right: 2px;
				border-color: transparent #fff transparent transparent;
			}
			
			.menu-1-icon .arrow-triangle {
				border-color: transparent transparent transparent #94caec;
			}
			
			.menu-1-icon:hover .arrow-triangle {
				border-color: transparent transparent transparent #fff;
			}
			
			.menu-list>li>a,
			.submenu>li>a,
			.submenu-3>li>a {
				display: block;
			}
			
			.menu-list li a {
				position: relative;
				-webkit-transition: all 0.6s ease;
				-moz-transition: all 0.6s ease;
				-o-transition: all 0.6s ease;
				-ms-transition: all 0.6s ease;
			}
			
			.menu-list li a,
			.menu-list li a:hover {
				text-decoration: none;
			}
			
			.menu-list>li>a {
				color: #FFF;
				padding: 12px 30px;
				overflow: hidden;
				text-align: left;
			}
			
			.menu-list>li>a:hover,
			.menu-list>li.active>a {
				background: #94caec;
			}
			
			.i-def-arrow {
				position: absolute;
				top: 22px;
				right: 30px;
				width: 8px;
				height: 14px;
				background: url(/ug/b/img/icon-more.png) no-repeat center;
				display: inline-block;
				-webkit-transition: -webkit-transform 0.6s ease;
				-moz-transition: -moz-transform 0.6s ease;
				-o-transition: -o-transform 0.6s ease;
				-ms-transition: -ms-transform 0.6s ease;
				transform-origin: 4px 3px;
				-webkit-transform: translate(0, -50%);
				-moz-transform: translate(0, -50%);
				-ms-transform: translate(0, -50%);
				-o-transform: translate(0, -50%);
				transform: translate(0, -50%);
			}
			
			.i-def-arrow.arrowRot {
				top: 19px;
				-webkit-transform: rotate(90deg);
				-moz-transform: rotate(90deg);
				-o-transform: rotate(90deg);
				-ms-transform: rotate(90deg);
				transform: rotate(90deg);
			}
			
			.submenu {
				overflow: hidden;
				height: 0;
				-webkit-transition: all 0.6s ease;
				-moz-transition: all 0.6s ease;
				-o-transition: all 0.6s ease;
				-ms-transition: all 0.6s ease;
			}
			
			.submenu>li>a,
			.submenu-3>li>a {
				color: #a6b7c6;
				position: relative;
			}
			
			.submenu>li>a {
				padding: 12px 35px;
				background: #0f6ca7;
			}
			
			.submenu>li>a:hover,
			.submenu>li.active a {
				color: #fff;
				background: #6ea2c3;
			}
			
			.submenu>li>a:hover:before,
			.submenu>li.active>a:before,
			.submenu-3>li>a:hover:before,
			.submenu-3>li.active>a:before {
				content: '';
				position: absolute;
				left: 0;
				top: 0;
				bottom: 0;
				width: 6px;
				background: #ffac44;
			}
			
			.submenu-3 {
				position: absolute;
				left: 220px;
				display: none;
			}
			
			.submenu-3>li>a {
				padding: 12px 30px;
				background: #053b5c;
				position: relative;
			}
			
			.submenu-3>li>a:hover,
			.submenu-3>li.active a {
				color: #fff;
				background: #537b94;
			}
			
			::-webkit-scrollbar {
				display: none;
			}
			/*左边菜单end*/
		</style>
		<block name="css"></block>
	</head>

	<body>
		<div class="console-body default-skin">
			<div class="header">
				<div class="header-nav">
					<div class="header-box">
						<div class="nav-left">
							<a class="logo fleft"> <i class="fleft"> </i> <span>{$company}</span>
							</a>
						</div>
						<div class="navbar-center">
							<block name="subtitle">微信公众号后台管理系统</block>
						</div>
						<block name="nav-right">
						<div class="nav-right">
							<ul class="right-box">
								<li>
									<a class="notice-box">
										<i class="glyphicon glyphicon-bell"></i>
										<span class="admin-badge"></span>
									</a>
								</li>
								<li>
									<a>
										<i class="icon-shop none"> 
											<img src="/ug/b/img/icon-shop.png">
										</i>
										<span>欢迎您，{$uname}</span>
									</a>
								</li>
								<li id="qrcodeHide" class="none">
									<a class="unbind-txt">绑定</a>
									<div id="qrcodeShow" class="qrcodeHide_box">
										<div class="qrcode-box"></div>
									</div>
								</li>
								<li id="unbind-btn">
									<a class="unbind-txt">解绑</a>
								</li>
								<li>
									<a id="btn-logout">
										<i class="icon-logout"></i>
										<span>退出</span>
									</a>
								</li>
							</ul>
						</div>
						</block>
					</div>
				</div>
			</div>
			<div class="bg-blue"></div>
			<div class="console-content">
				<!--<div class="menu-wrap">
					<ul class="menu-list" id="menu_container"></ul>
					<div class="bg-blue"></div>
				</div>-->
				<div class="l-menu-aside">
				<div class="l-menu-1">
					<div class="menu-1-icon">
						<span class="nav-glyph">
							<i class="icon-bar top"></i>
							<i class="icon-bar middle"></i>
							<i class="icon-bar bottom"></i>
						</span>
						<i class="arrow-triangle"></i>
					</div>
				</div>
				<ul class="menu-list" id="menu_container"></ul>
			</div>
				<!--menu-wrap end-->
				<div class="content-page">
					<div class="cont-top">
						<div class="cont-top-bg"></div>
						<div class="cont-top-l" id="tab-backward" style="cursor: pointer;">
							<i class="icon icon-return"></i><span class="border-r"></span>
						</div>
						<div class="cont-top-mid">
							<ul class="cont-tab-list" id="tab_container"></ul>
						</div>
						<!--cont-top-mid end-->
						<div class="cont-top-r">
							<div id="tab-forward" style="cursor: pointer;">
								<span class="border-l"></span> <i class="icon icon-go fleft"></i> <span class="border-r"></span><i id="btn-refresh" class="icon icon-refresh"></i>
							</div>
							<div class="tab-operat" style="cursor: pointer;">选项卡操作</div>
							<ul class="tab-other-opration-dropdown flag-click-hide" style="display: none">
								<li class="tab-opration-menu" id="tab-position">
									<a><i class="fa fa-map-marker"></i> 定位当前选项卡</a>
								</li>
								<li class="divider"></li>
								<li class="tab-opration-menu" id="tab-refresh">
									<a><i class="fa fa-refresh"></i> 刷新当前选项卡</a>
								</li>
								<li class="divider"></li>
								<li class="tab-opration-menu" id="tab-closeall">
									<a><i class="fa fa-times-circle-o"></i> 关闭全部选项卡</a>
								</li>
								<li class="tab-opration-menu" id="tab-closeother">
									<a><i class="fa fa-window-close"></i> 关闭其他选项卡</a>
								</li>
							</ul>
						</div>
					</div>
					<!--cont-top-r end-->
					<div class="page-wrap" id="ifr_container">
						<!--放入每个页面的内容-->
					</div>
				</div>
				<!--content-page end-->
			</div>
			<div id="noiceShow" class="noice-wrap"></div>
			<div id="unbindbox" style="display: none;">
				<div>
					<div>确认解绑吗</div>
				</div>
			</div>
			<!--console-content end-->
		</div>
		<block name="append"></block>
		<script type="text/javascript" src="/ug/js/jquery.min.js"></script>
		<script src="/ug/js/jquery.qrcode.min.js"></script>
		<script type="text/javascript" src="/ug/js/plugincore.js"></script>
		<script type="text/javascript" src="/ug/b/js/dialog.js"></script>
		<!--<script type="text/javascript" src="/ug/b/js/menu_tab.js"></script>-->
		<script type="text/javascript" src="/ug/b/js/menu_tab_1.js"></script>
		<script type="text/javascript" src="/ug/admin/js/ws.js"></script>
		<script type="text/javascript">
			$(function() {
				var isBind = "{$bind}"
				$.menuTab({
					data: {$menudata},
					menus: $('#menu_container'),
					tabs: $('#tab_container'),
					ifrs: $('#ifr_container'),
					onItem: null,
					button: {
						backward: $('#tab-backward'),
						forward: $('#tab-forward'),
						closeAll: $('#tab-closeall'),
						closeOther: $('#tab-closeother'),
						position: $('#tab-position'),
						refresh: $('#tab-refresh,#btn-refresh'),
						callback: function(node) {
							node.parent('.flag-click-hide').hide();
						}
					},
				});

				$.openWin = function() {
					window.open.apply(window, arguments);
				};
				$.openTab = function(url, title, tag) {
					return $.menuTab().openTab(url, title, tag);
				};
				$.setTabTitle = function(title) {
					return $.menuTab().setTabTitle(title);
				};
				$.closeTab = function(tag) {
					return $.menuTab().closeTab(tag);
				};
				$.entryTab = function() {
					return $.menuTab().entryTab();
				};
				$.openInf = function() {
					return $.menuTab().openInf.apply($.menuTab(), arguments);
				};
				$('.tab-operat').on('click', function() {
					var tools = $('.tab-other-opration-dropdown');
					if(tools.is(':visible')) {
						tools.hide();
					} else {
						tools.show();
					}
				});

				function outsideRange(e, node) {
					e = e || window.event;
					var x = e.pageX || e.clientX + document.body.scroolLeft;
					var y = e.pageY || e.clientY + document.body.scrollTop;
					var off = node.offset();
					if(x < off.left || y < off.top || x > off.left + node.width() || y > off.top + node.height()) {
						return true;
					}
					return false;
				}

				$('.tab-other-opration-dropdown').on('mouseout', function(e) {
					e.preventDefault();
					e.stopPropagation();
					if(outsideRange(e, $(this))) {
						$(this).hide();
					}
				});

				$('#btn-logout').on('click', function(e) {
					e.preventDefault();
					e.stopPropagation();
					$.ajaxExt('{$url.logout}', '正在退出，请稍候...', function(jo) {
						if(jo.isOk()) {
							window.location.reload();
						} else {
							$.alert(jo.getMessage());
						}
					});
				});

				$('#menu_container li').each(function() {
					$(this).children('.flag-menu:first').trigger('click');
					if($(this).attr('_leaf') == '1') {
						return false;
					}
				});
				//扫一扫
				$.ws(true, "{$wsuri}", function(data) {
					if(data.u) {
						window.location.reload();
					} else {
						$('.msg-err').show();
						$('.msg-err-title').show();
					}
				}, function(id) {
					$('.qrcode-box').html('').qrcode({
						text: "{$url.wxqrbind}" + '?cd=' + id + '&u=' + "{$uid}",
						render: "canvas",
						width: 100, //宽度
						height: 100, //高度
						typeNumber: -1,
						background: '#FFFFFF',
						foreground: '#000000',
					});
					$('.qrcode-box').show();
				}, function() {
					$('.qrcode-box').hide();
				});

				$('#unbind-btn').on('click', function() {
					$('#unbindbox').dialog('提示', function() {
						var turl = "{$url.unqrbind}";
						var pdata = {};
						$.ajaxExt(turl, pdata, function(jo) {
							if(jo.isOk()) {
								$.alert('解绑成功');
								window.location.reload();
							} else {
								$.alert(jo.getMessage());
							}
						});
					});
				});
				isbind(parseInt(isBind));
				var timer = null;
				$('.notice-box').on('mouseenter', function(ev) {
					if(parseInt($('.notice-box>.admin-badge').text() || $('.notice-box>.admin-badge').text()=='')<=0) return;
					var l = this.offsetLeft-this.offsetWidth/2;
					var t = this.offsetTop + this.offsetHeight;
					$('#noiceShow').css({
						'left': l + 'px',
						'top': t + 'px',
						'display': 'block'
					});
					return false;
				});
				$('.notice-box').on('mouseleave', function(ev) {
					timer = setTimeout(function() {
						$('#noiceShow').css({
							'display': 'none'
						});
					}, 200);
					return false;
				});
				$(document).on('mouseenter', '#noiceShow', function() {
					clearTimeout(timer);
					return false;
				});
				$(document).on('mouseleave', '#noiceShow', function() {
					$('#noiceShow').css({
						'display': 'none'
					});
					return false;
				});
				htmlnotice();
			});
			function htmlnotice () {
				var datas=[{
					id:"1",
					title:"aaaaaaaaaaaaaaaaaaaaaaaaa",
					detail_time:"2018-02-03&nbsp;10:10:00"
				},{
					id:"2",
					title:"aaaaaaaaaaaaaaaaaaaaaaaaa",
					detail_time:"2018-02-03&nbsp;10:10:00"
				},{
					id:"3",
					title:"aaaaaaaaaaaaaaaaaaaaaaaaa",
					detail_time:"2018-02-03&nbsp;10:10:00"
				},{
					id:"4",
					title:"aaaaaaaaaaaaaaaaaaaaaaaaa",
					detail_time:"2018-02-03&nbsp;10:10:00"
				},{
					id:"1",
					title:"aaaaaaaaaaaaaaaaaaaaaaaaa",
					detail_time:"2018-02-03&nbsp;10:10:00"
				},{
					id:"2",
					title:"aaaaaaaaaaaaaaaaaaaaaaaaa",
					detail_time:"2018-02-03&nbsp;10:10:00"
				},{
					id:"3",
					title:"aaaaaaaaaaaaaaaaaaaaaaaaa",
					detail_time:"2018-02-03&nbsp;10:10:00"
				},{
					id:"4",
					title:"aaaaaaaaaaaaaaaaaaaaaaaaa",
					detail_time:"2018-02-03&nbsp;10:10:00"
				},{
					id:"1",
					title:"aaaaaaaaaaaaaaaaaaaaaaaaa",
					detail_time:"2018-02-03&nbsp;10:10:00"
				}];
				datas = {$notice ? $notice:'[]'};
				var html='';
				if(datas && datas.length>0){
					var len = datas.length>9?'...':datas.length;
					$('.notice-box>.admin-badge').html(len).show();
					html+='<div class="notice-title">';
					html+='	<div class="fleft">消息中心</div>';
					html+='	<a class="fright" href="#">消息管理</a>';
					html+='</div>';
					html+='<ol class="noice-list">';
					if(datas.length>1){
						for(var i=0;i<5;i++){
							var item=datas[i];
							html+='	<li>';
							html+='		<div class="notice-info">'+item.title+'</div>';
							html+='		<div class="notice-time">'+item.ctime+'</div>';
							html+='	</li>';
						}
					}else{
						html+='	<li>';
						html+='		<div class="notice-info">'+datas[0].title+'</div>';
						html+='		<div class="notice-time">'+datas[0].ctime+'</div>';
						html+='	</li>';
					}
					html+='</ol>';
					html+='<a class="notice-view-more" href="javascript:;" onclick="notice()">查看更多</a>';
					$('#noiceShow').html(html);
					$('#noiceShow').show();
				}else{
					$('.notice-box>.admin-badge').hide();
					$('#noiceShow').hide();
				}
			}

			function isbind(isBind) {
//                $('#qrcodeHide').removeClass('none');
//                $('#unbind-btn').addClass('none');
				if(isBind) {
					$('#qrcodeHide').addClass('none');
					$('#unbind-btn').removeClass('none');
				} else {
					$('#qrcodeHide').removeClass('none');
					$('#unbind-btn').addClass('none');
				}
			}
			function notice(){
				$.openTab("/Admin/notice", '通知管理');
			}

		</script>
	</body>
	<block name="js">
	</block>

</html>