<extend name="Base@Admin/listtable" />
<block name="title">
<title>功能列表</title>
</block>
<block name="js"> <script type="text/javascript">
	inf_dtbl.cols = [ {
		col : 'c0',
		tit : '角色名',
		rep : function(d) {
			var html = '';
			html += '<input type="text" value="' + d.c0.html2escape() + '" class="form-control anchor_c0" cid="c0" >';
			return html;
		}
	}, {
		col : 'c1',
		tit : '角色状态',
		rep : function(d) {
			var html = '';
			html += '<div class="checkbox">';
			html += '<label> <input type="checkbox" value="1" class="anchor_c1" cid="c1"' + (d.c1 == '1' ? ' checked' : '') + '> 启用</label>';
			html += '</div>';
			return html;
		}
	}, {
		col : 'c2',
		tit : '角色说明',
		rep : function(d) {
			var html = '';
			html += '<textarea class="form-control anchor_c2" cid="c2" rows="1">' + d.c2.html2escape() + '</textarea>';
			return html;
		}
	}, {
		col : 'c3',
		tit : '创建时间',
		sortable : true,
		rep : function(d) {
			if (d.c3 != '0')
				return d.c3.date('yyyy-MM-dd hh:mm:ss');
		}
	}, ];
	
	inf_dtbl.menus.rows.push({
		tag : 'toRelation',
		title : '配置权限',
		icon : 'glyphicon glyphicon-paperclip'
	});
	
	inf_ext.menus.callback = function(tag, d){
		switch(tag){
		case 'toRelation':
			$.openTab('{:U('roleperm')}?id='+d.id, d.c0 + ' 权限');
			break;
		default :
			break;
		}
	};
	
	$(function() {
		$('#btn_add').on('click', function() {
			$('#adddlg').dialog('新增角色', addSave);
		});
	});

	function formIgnore(k, pdata) {
		switch (k) {
		case 'c1':
			pdata[k] = 0;
			return true;
		}
	}

	function addSave() {
		var pdata = {};
		$.formvals($(this), 'cid', pdata);
		for ( var k in pdata) {
			if (pdata[k] === '' || pdata[k] === '0') {
				if (k == 'c1') {
					pdata[k] = '0';
				} else {
					var anchor = $(this).find('.anchor_' + k);
					anchor.poptip('请输入或选择信息');
					anchor.trigger('focus');
					return false;
				}
			}
		}
		var turl = '{$url.addsave}';
		$.ajaxExt(turl, pdata, function(jo) {
			if (jo.isOk()) {
				window.location.reload();
			} else {
				$.alert(jo.getMessage());
			}
		});
	}
</script> </block>
<block name="query_ui">
<div class="form-group">
	<!--<label>角色名</label> --><input type="text" class="form-control input-sm" cid="q{$k++}" placeholder="搜索角色名">
</div>
</block>
<block name="tool_box">
<button id="btn_add" type="button" class="btn btn-sm btn-default admin-btn-tab">
	<span class="glyphicon glyphicon-plus"></span>&nbsp;新增
</button>
</block>
<block name="free_area">
<div id="adddlg" style="display: none;">
	<div>
		<div>
			<form class="form-horizontal">
				<div class="form-group">
					<label class="col-sm-4 control-label">角色名</label>
					<div class="col-sm-8">
						<input type="text" class="form-control anchor_c0" cid="c0">
					</div>
				</div>
				<div class="form-group">
					<label class="col-sm-4 control-label">角色状态</label>
					<div class="col-sm-8">
						<div class="checkbox">
							<label> <input type="checkbox" value="1" class="anchor_c1" cid="c1"> 启用
							</label>
						</div>
					</div>
				</div>
				<div class="form-group">
					<label class="col-sm-4 control-label">角色说明</label>
					<div class="col-sm-8">
						<textarea class="form-control anchor_c2" cid="c2" rows="3"></textarea>
					</div>
				</div>
			</form>
		</div>
	</div>
</div>
</block>