<!DOCTYPE html>
<html lang="zh-CN">

	<head>
		<meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
		<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=no" />
		<block name="title">
			<title>登录汉高微信后台管理</title>
		</block>
		<link rel="stylesheet" href="/ug/b/css/common.css">
		<link rel="stylesheet" href="/ug/b/css/default-skin.css">
		<link rel="stylesheet" href="/ug/b/css/modal-box.css">
		<link rel="stylesheet" href="/ug/b/css/login.css">
		<style>
			.login-innero {
				top: 50%;
				left: 0;
				-webkit-transform: translate(0, -50%);
				transform: translate(0, -50%);
				position: absolute;
				z-index: 22;
				right: 10%;
			}

			.login-box-lo {
				width: 76%;
				height: 100%;
			}

			.login-box-lo>img {
				width: 80%;
				vertical-align: middle;
			}

			.login-box-ro {
				color: #fff;
				border-style: solid;
				border-width: 1px;
				border-color: rgba(0, 0, 0, 0.19);
				border-radius: 5px;
				background-color: rgb(255, 255, 255);
				box-shadow: 0px 5px 16px 0px rgba(0, 0, 0, 0.75);
				padding: 14px 16px 36px 16px;
				width: 324px;
				height: 296px;
				top: 50%;
				right: 0;
				-webkit-transform: translate(0, -50%);
				transform: translate(0, -50%);
				position: absolute;
				z-index: 1000;
			}

			.switch-box {
				width: 64px;
				height: 64px;
				position: absolute;
				top: 12px;
				right: 12px;
				list-style: none;
			}

			.switch-box img.switch-img {
				width: 100%;
			}

			.login-tip {
				position: absolute;
				top: 26px;
				right: 65px;
			}

			.poptip {
				border: 1px solid #f3d995;
				height: 16px;
				line-height: 16px;
				padding: 6px 8px;
				background: #fefcee;
				position: relative;
			}

			.poptip-arrow {
				top: 8px;
				right: 0;
				position: absolute;
				z-index: 10;
			}

			.poptip-arrow em,
			.poptip-arrow span {
				position: absolute;
				width: 0;
				height: 0;
				border-color: rgba(255, 255, 255, 0);
				border-color: transparent \0;
				border-style: solid;
				overflow: hidden;
				top: 0;
				left: 0;
			}

			.poptip-arrow em {
				top: 0;
				left: 1px;
				border-left-color: #f3d995;
				border-width: 6px 0 6px 6px;
			}

			.poptip-arrow span {
				border-left-color: #fefcee;
				border-width: 6px 0 6px 6px;
			}

			.poptip .poptip-content {
				color: #df9c1f;
				font-size: 12px;
				font-weight: 400;
			}

			.ul-wrap {
				padding: 32px 18px 0
			}

			.qrcode-main {
				position: relative;
				width: 146px;
				height: 146px;
				margin: 20px auto;
			}

			.msg-err {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 146px;
				background-color: #000;
				opacity: .7;
				filter: alpha(opacity=70);
				display: none;
			}

			.msg-err-title {
				color: #fff;
				top: 50%;
				width: 100%;
				text-align: center;
				-webkit-transform: translate(0, -50%);
				transform: translate(0, -50%);
				position: absolute;
				z-index: 1000;
				display: none;
			}

			.qrcode-desc {
				text-align: center;
			}

			.qrcode-desc>img {
				display: inline-block;
				vertical-align: bottom;
			}

			.qrcode-txt {
				font-size: 16px;
				color: rgb(155, 155, 154);
				text-align: left;
				display: inline-block;
			}

			.qrcode-txt>i {
				color: rgb(52, 132, 221);
				font-size: inherit;
				font-style: normal;
			}

			.multi-info {
				overflow: auto;
				height: 200px;
			}

			.multi-info<h4 {
				font-size: 17px;
				color: rgb(163, 162, 162);
			}

			.multi-info>li {
				width: 100%;
				font-size: 18px;
				color: #000;
				padding: 18px 0;
				text-align: center;
			}

			.multi-info>li.active {
				color: #1989D1;
			}

			.multi-info>li:hover {
				background-color: rgb(199, 226, 255);
			}

			.none {
				display: none;
			}
		</style>
		<script type="text/javascript">
			if(self !== top) {
				top.window.location.reload();
			}
		</script>
		<block name="css"></block>
	</head>

	<body>
		<div class="default-skin">
			<div class="login-header">
				<div class="login-header-nav">
					<div class="nav-left">
						<a class="logo-big fleft"> <i class="fleft"></i> <span><block name="subtitle">欢迎登录汉高微信管理系统</block></span>
						</a>
					</div>
				</div>
			</div>
			<div class="login-body">
				<div class="login-cont">
					<div class="login-innero">
						<div class="login-box-lo">
							<img src="/ug/b/img/bg-login.jpg" />
						</div>
						<div class="login-box-ro">
							<div class="login-title">
								<h3>用户登录</h3>
							</div>
							<ul class="switch-box">
								<li class="switch-img1">
									<img class="switch-img" src="/ug/admin/img/icon-qrcode.png" />
								</li>
								<li class="switch-img2 none">
									<img class="switch-img" src="/ug/admin/img/icon-complete.png" />
								</li>
							</ul>
							<div class="login-tip">
								<div class="poptip">
									<div class="poptip-arrow">
										<em></em>
										<span></span>
									</div>
									<div class="poptip-content">二维码登录在这里</div>
								</div>
							</div>
							<div class="ul-wrap">
								<ul class="login-ul">
									<li>
										<div class="field-input-wrap">
											<input id="username" type="text" class="field-input" autocomplete="off" placeholder="请输入帐号">
											<div class="field-icon icon-user"></div>
										</div>
									</li>
									<li>
										<div class="field-input-wrap">
											<input id="password" type="password" class="field-input password" id="password" autocomplete="off" placeholder="请输入密码">
											<div class="field-icon icon-pass"></div>
										</div>
										<div class="forget-box">
											<span class="forget-tip">忘记密码了？</span>
										</div>
									</li>
									<li>
										<div class="field-group">
											<div class="field-input-wrap w50">
												<input id="vcode" type="text" maxlength="4" class="set-code-input" autocomplete="off" placeholder="请输入验证码">
											</div>
											<div class="field-code-wrap">
												<div class="set-img-code fleft">
													<img id="vcimg" />
												</div>
												<i class="icon btn-refresh fright"></i>
											</div>
										</div>
										<div class="tips">&nbsp;</div>
									</li>
								</ul>
								<div class="multi-box"></div>
								<div class="button-box">
									<a id="login" class="button-big button-big-bg">登录</a>
								</div>
							</div>
							<div class="qrcode-wrap none">
								<div class="qrcode-main">
									<div class="qrcode-box"></div>
									<div class="msg-err"></div>
									<div class="msg-err-title">无权限登录</div>
								</div>
								<div class="qrcode-desc">
									<img src="/ug/admin/img/icon-scan.png" />
									<span class="qrcode-txt">
										打开<i>手机微信</i><br />
										扫一扫<i>登录</i>
									</span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="footer">
				<ul class="footer_menu">
					<li>杭州汉高信息科技有限公司</li>
					<li>Copyright© 2003 - 2017 hencego.com</li>
					<li>
						<a href="http://www.miibeian.gov.cn/">浙ICP备05073380号</a>
					</li>
				</ul>
			</div>
		</div>
		<block name="append"></block>
	</body>
	<script type="text/javascript" src="/ug/js/jquery.min.js"></script>
	<script src="/ug/js/jquery.qrcode.min.js"></script>
	<script type="text/javascript" src="/ug/js/plugincore.js"></script>
	<script type="text/javascript" src="/ug/b/js/dialog.js"></script>
	<script type="text/javascript" src="/ug/b/js/datatable.js"></script>
	<script type="text/javascript" src="/ug/admin/js/ws.js"></script>
	<script type="text/javascript">
		$(function() {
		    initQRcode();
			$('.field-input-wrap > input').on('focus', setFocus);
			$('.field-input-wrap > input').on('blur', cancleFocus);
			$('#login').on('click', function() {
				submitForm();
			});
			$('#vcimg,.btn-refresh').on('click', function() {
				$('#vcimg').attr('src', '/Tp/Verify/' + '?r=' + Math.random());
				$('#vcode').focus();
			});
			document.onkeydown = function(e) {
				var ev = document.all ? window.event : e;
				if(ev.keyCode == 13) {
					submitForm();
				}
			};
			$('.btn-refresh').trigger('click');
			$('#username').focus();
			$('.switch-box>li.switch-img1').on('click', function() {
				$('.ul-wrap,.login-tip,.switch-box>li.switch-img1').addClass('none');
				$('.qrcode-wrap,.switch-box>li.switch-img2').removeClass('none');
				$('.login-title>h3').text('扫码登录');
			});
			$('.switch-box>li.switch-img2').on('click', function() {
				$('.qrcode-wrap,.switch-box>li.switch-img2').addClass('none');
				$('.ul-wrap,.login-tip,.switch-box>li.switch-img1').removeClass('none');
				$('.login-title>h3').text('用户登录');
			});
			$('.multi-info>li').on('click', function() {
				$(this).addClass('active').siblings().removeClass('active');
			});
			//扫一扫
			$.ws(true, "{$wsuri}", function(data) {
			    if(!data.m){
                    $('.msg-err').show();
                    $('.msg-err-title').show();
				}else{
                    var pdata = {
                        t: data.m,
                        u: data.u,
                    };
                    $.ajaxExt("{$url.loginAj}", pdata, function(jo) {
                        if(jo._c == 0) {
                            window.location.reload();
                        } else {
                            $('.msg-err').show();
                            $('.msg-err-title').show();
                        }
                    });
				}
			}, function(id) {
				$('.qrcode-box').html('').qrcode({
					text: "{$url.qrlogin}" + '?cd=' + id,
					render: "canvas",
					width: 146, //宽度
					height: 146, //高度
					typeNumber: -1,
					background: '#FFFFFF',
					foreground: '#000000',
				});
				$('.qrcode-box').show();
			}, function() {
				$('.qrcode-box').hide();
			});
		});

		function initQRcode () {
            $('.ul-wrap,.login-tip,.switch-box>li.switch-img1').addClass('none');
            $('.qrcode-wrap,.switch-box>li.switch-img2').removeClass('none');
        }

		function setFocus() {
			$(this).css('border', 0);
			$(this).closest('.field-input-wrap').addClass('focus');
		}

		function cancleFocus() {
			$(this).css('border', 0);
			$(this).closest('.field-input-wrap').removeClass('focus');
		}

		function checkInfo() {
			var pdata = {};
			pdata.username = $('#username').val();
			if(pdata.username == '') {
				$('#username').focus();
				return '请输入帐号';
			}
			pdata.password = $('#password').val();
			if(pdata.password == '') {
				$('#password').focus();
				return '请输入密码';
			}
			pdata.vcode = $('#vcode').val();
			if(pdata.vcode == '') {
				$('#vcode').focus();
				return '请输入验证码';
			}
			return pdata;
		}

		function submitForm() {
			var pdata = checkInfo();
			if(typeof pdata == 'string') {
				$('.tips').html(pdata);
				return;
			}
			$('.tips').html('&nbsp;');
			$.ajaxExt('{$url.login}', pdata, function(jo) {
				if(jo._c == 0) {
					if(jo.tourl) {
						window.location.replace(jo.tourl);
					} else {
						window.location.reload();
					}
				} else {
					$('.tips').html(jo._m);
					$('#vcode').val('');
					$('.btn-refresh').trigger('click');
					if(jo._c == 5) {
						$('#vcode').focus();
					}
				}
			});
		}
	</script>
	<block name="js"></block>

</html>